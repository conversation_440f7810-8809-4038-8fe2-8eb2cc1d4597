{{ config(
    materialized = 'incremental',
    unique_key = 'order_no',
    parition_by = 'modified_year',
    file_format = 'delta',
    incremental_strategy='merge',
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
    ) }}

{% if is_incremental() %}
    SELECT
        order_no,
        order_time,
        shop_id,
        owner_id,
        warehouse_code,
        express_code,
        express_no,
        goods_total,
        sku_total,
        order_status,
        question_status,
        payment_time,
        created,
        modified,
        delivery_time,
        oms_delivery_time,
        rejection_status,
        question_reason,
        latest_delivery_time,
        logistics_intercept_status,
        is_sh_ship,
        copy_order_reason,
        is_replace,
        YEAR(modified) AS modified_year
    FROM
        {{ ref ("order_status") }}
    WHERE
        modified >= (SELECT MAX(modified) FROM {{ this }} WHERE modified_year >= 2023)
{% else %}
WITH unioned AS(
    SELECT
        order_no,
        order_time,
        shop_id,
        owner_id,
        warehouse_code,
        express_code,
        express_no,
        goods_total,
        sku_total,
        order_status,
        question_status,
        payment_time,
        created,
        modified,
        delivery_time,
        oms_delivery_time,
        rejection_status,
        question_reason,
        latest_delivery_time,
        logistics_intercept_status,
        is_sh_ship,
        copy_order_reason,
        is_replace,
        YEAR(modified) AS modified_year
    FROM {{ ref ("order_status") }}
    UNION
    SELECT
        order_no,
        order_time,
        shop_id,
        owner_id,
        warehouse_code,
        express_code,
        express_no,
        goods_total,
        sku_total,
        order_status,
        question_status,
        payment_time,
        created,
        modified,
        delivery_time,
        oms_delivery_time,
        rejection_status,
        question_reason,
        latest_delivery_time,
        logistics_intercept_status,
        is_sh_ship,
        copy_order_reason,
        is_replace,
        YEAR(modified) AS modified_year
    FROM {{ ref ("order_status_his") }}
),
unic AS(
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY order_no ORDER BY modified DESC) AS row_no
    FROM
        unioned
)

SELECT
    --Disable parsing error for the rest of file
    --noqa: disable=PRS
    * EXCEPT(row_no)
    --noqa: enable=all
FROM
    unic
WHERE
    row_no = 1
{% endif %}
