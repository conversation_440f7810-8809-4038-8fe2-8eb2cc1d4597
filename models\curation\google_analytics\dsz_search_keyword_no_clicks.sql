{{ config(
    materialized='table',
    indexes=[{'columns': ['keywords']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    search_count,
    `keywords`,
    `event_date`,
    ga_version
FROM (
    SELECT
        COUNT(*) AS search_count,
        `keywords`,
        `event_date`,
        "ua" AS ga_version
    FROM {{ ref("dsz_ua_search_keyword") }} WHERE deatil_views = 0 GROUP BY `keywords`, `event_date`
    UNION
    SELECT
        COUNT(*) AS search_count,
        `keywords`,
        `event_date`,
        "ga4" AS ga_version
    FROM {{ ref("dsz_ga4_search_keyword") }} WHERE deatil_views = 0 GROUP BY `keywords`, `event_date`
)
