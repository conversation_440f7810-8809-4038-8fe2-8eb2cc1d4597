{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH employee_id AS (
    SELECT DISTINCT
        id,
        external_id
    FROM {{ ref("crawler_eh_employee_list_raw") }}
    WHERE external_id IS NOT NULL
),

ranked_results AS (
    SELECT
        a.id,
        a.total_hours AS totalhours,
        b.external_id AS employeeid,
        a.leave_balance_amount,
        a.leave_category_name AS leavecategory,
        a.start_date AS fromdate,
        a.end_date AS todate,
        a.status,
        a.ingestion_time_utc,
        -- we have identified duplicate records in the source system
        -- as reported in  https://newaim-au.atlassian.net/browse/DA-6737 
        -- the use of rank() is to get the latest record for each leave request record
        -- and to remove the duplicates
        ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY a.ingestion_time_utc DESC) AS _row_number
    FROM {{ ref("employmenthero_leaverequest_raw") }} AS a
        LEFT JOIN employee_id AS b ON a.employee_id = b.id
)

SELECT
    id,
    totalhours,
    employeeid,
    leave_balance_amount,
    leavecategory,
    fromdate,
    todate,
    status,
    ingestion_time_utc
FROM ranked_results
WHERE _row_number = 1
