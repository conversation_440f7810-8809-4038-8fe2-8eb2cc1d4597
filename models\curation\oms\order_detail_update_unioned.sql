{{ config(
    materialized = 'incremental',
    unique_key = 'id',
    incremental_strategy='merge',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('order_detail_update') }}
{% if is_incremental() %}
    SELECT
        id,
        order_no,
        action,
        detail_id,
        old_sku,
        old_package_sku,
        old_num,
        new_sku,
        new_package_sku,
        new_num,
        user_name,
        created
    FROM {{ ref('order_detail_update') }}
    WHERE created >= (SELECT MAX(created) FROM {{ this }})
{% else %}
    SELECT
        id,
        order_no,
        action,
        detail_id,
        old_sku,
        old_package_sku,
        old_num,
        new_sku,
        new_package_sku,
        new_num,
        user_name,
        created
    FROM {{ get_source('lake', 'order_detail_update_his') }}
    UNION
    SELECT
        id,
        order_no,
        action,
        detail_id,
        old_sku,
        old_package_sku,
        old_num,
        new_sku,
        new_package_sku,
        new_num,
        user_name,
        created
    FROM {{ get_source('lake','order_detail_update_partition') }}
{% endif %}
