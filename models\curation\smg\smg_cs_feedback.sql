{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH uniq AS (
    SELECT
        id,
        f_comment_date,
        f_user_id,
        f_replied_date,
        IF(f_is_ignore = 1, TRUE, FALSE) AS is_ignore,
        f_comment_type AS comment_type,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(f_replied_date, f_comment_date) DESC) AS row_id
    FROM
        {{ ref('dl_cs_feedback') }}
)

SELECT
    id,
    f_comment_date,
    is_ignore,
    comment_type,
    f_user_id,
    f_replied_date,
    COALESCE(f_replied_date, f_comment_date) AS watermark
FROM
    uniq
WHERE
    row_id = 1
