{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        a.id,
        a.f_sender AS sender,
        a.f_user_id AS user_id,
        a.f_subject AS reference_id,
        FROM_UTC_TIMESTAMP(FROM_UNIXTIME(a.f_receive_date), 'Australia/Melbourne') AS receive_aest,
        FROM_UTC_TIMESTAMP(
            FROM_UNIXTIME(
                GREATEST(b.replied_unix, a.f_ignore_date)
            ),
            'Australia/Melbourne'
        ) AS replied_aest,
        GREATEST(
            a.f_receive_date,
            b.replied_unix,
            a.f_ignore_date
        ) AS watermark,
        ROW_NUMBER() OVER (
            PARTITION BY a.id ORDER BY GREATEST(a.f_receive_date, b.replied_unix, a.f_ignore_date) DESC
        ) AS row_no
    FROM
        {{ ref('dl_cs_message') }} AS a
        INNER JOIN
            {{ ref('smg_cs_message_outbox') }} AS b
            ON
                a.f_message_id = b.reference_id
        LEFT JOIN
            {{ ref('dl_cs_message_operation') }} AS c
            ON
                a.id = c.f_message_id
)

SELECT *
FROM
    uniq
WHERE
    row_no = 1
