{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('combo_base_bridge') }}
-- depends_on: {{ ref('na_product') }}
-- depends_on: {{ ref('base_sku_replace') }}
-- depends_on: {{ ref('current_base_sku_stock') }}

SELECT
    a.combined_sku,
    a.ean,
    a.created_at,
    a.common_listing,
    {{ base_sku_replacement_condition('a', 'c', 'd', 'e') }},
    a.status,
    a.ab_kit,
    a.qty_per_base
FROM
    {{ ref('combo_base_bridge') }} AS a
    INNER JOIN
        {{ ref('na_product') }} AS c
        ON
            a.base_sku = c.sku
    LEFT JOIN
        {{ ref ('current_base_sku_stock') }} AS d
        ON
            a.base_sku = d.sku
    LEFT JOIN
        {{ ref('base_sku_replace') }} AS e
        ON
            a.base_sku = e.sku_from
