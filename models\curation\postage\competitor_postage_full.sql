{{ config(
        materialized = "incremental",
        partition_by = "ingestion_date",
        unique_key = "id",
        incremental_strategy = "merge",
        tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    CONCAT_WS("-", cp.channel, cp.pid, ep.postcode, cp.ingestion_date) AS id,
    ep.postcode,
    cp.* EXCEPT (postcode, response_status, id),
    cp.postcode IS NOT NULL AS reference_postcode
FROM {{ ref('ebay_postcode') }} AS ep
    LEFT JOIN {{ ref('competitor_postage_raw') }} AS cp ON ep.region_l2 = cp.region_l2
WHERE
    cp.response_status = 200
    {% if is_incremental() %}
        AND cp.ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
    {% endif %}
