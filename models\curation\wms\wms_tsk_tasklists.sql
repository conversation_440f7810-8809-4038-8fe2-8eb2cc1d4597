{{ config(
    materialized = 'incremental',
    unique_key='TaskId',
    incremental_strategy='merge',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH unioned AS (
    SELECT DISTINCT * EXCEPT (edittime_date)
    FROM
        {{ get_source("lake","wms_tsk_tasklists_partition") }}
    {% if is_incremental() %}
        WHERE
            edittime_date >= (
                SELECT MAX(edittime_date) FROM {{ get_source("lake","wms_tsk_tasklists_partition") }}
                WHERE edittime_date >= DATE_SUB(CURRENT_DATE(), 5)
            )
    {%else%} 
    UNION
    SELECT DISTINCT
        *        
    FROM {{ get_source("lake","wms_tsk_tasklists_history") }}
{% endif %}
),

uniq AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY taskid ORDER BY ingestion_time_utc DESC) AS row_id
    FROM
        unioned
)

SELECT
    --Disable parsing error for the rest of file
    --noqa: disable=PRS
    TRIM(docno) AS docno,
    * EXCEPT (row_id, docno)
--noqa: enable=all
FROM
    uniq
WHERE
    row_id = 1
