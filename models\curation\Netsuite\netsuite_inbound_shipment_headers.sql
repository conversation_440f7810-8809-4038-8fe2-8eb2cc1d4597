{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

--depend on: {{ ref ('netsuite_inbound_shipment') }}

WITH inbship_customfield AS (
    SELECT
        shipmentnumber AS sn,
        custrecord_na_vesselname AS vessel_name,
        --noqa: disable=PRS
        custrecord_na_urgentstatus :name AS urgent_status,
        custrecord_na_totalvolumeincbm AS total_volume_cbm,
        custrecord_na_spontime AS spontime,
        custrecord_na_shippedvolume AS actual_volum_cbm,
        custrecord_na_shippedcontainertype :name AS shipped_container_type,
        custrecord_na_sealnumber AS seal_number,
        --,custrecord_na_readydate as confirmed_ready_date
        to_date(from_utc_timestamp(custrecord_na_readydate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS confirmed_ready_date,
        custrecord_na_qcstatus :name AS qc_status,
        custrecord_na_portoforigin AS contract_port_of_origin,
        custrecord_na_portofdestination AS contract_port_of_destination,
        custrecord_na_loadingtype :name AS container_loadingtype,
        custrecord_na_intransitsubstatus :name AS intransit_substatus,
        custrecord_na_inbound_substatus :name AS to_be_shipped_substatus,
        custrecord_na_freightrate AS freight_rate,
        custrecord_na_freightforwarder :name AS freight_forwarder,
        ----ETD Week Number is calculated by NetSuite. It is based on Confirmed Ready Date (CRD) and the user Access Time of Inbound Shipment. The formula is listed as follows:
        ----IF weeknum(CRD)-weeknum(Access time)>=2, displays "Week" & Weeknum(CRD+5)/Weeknum( CRD+12)
        ----For example: CRD=2022-9-30, displays as Week 41/42 (Sunday 09/10 - Saturday 22/10)
        ----IF weeknum(CRD)-weeknum(Access time)<2, displays "Week" & Weeknum(Access time)+2/Weeknum(Access time)+3
        custrecord_na_etdweeknumber AS etd_week_number,
        to_date(from_utc_timestamp(custrecord_na_etd, 'Australia/Melbourne'), 'yyyy-mm-dd') AS estimated_departure_date,
        to_date(from_utc_timestamp(custrecord_na_eta, 'Australia/Melbourne'), 'yyyy-mm-dd') AS estimated_arrival_date,
        custrecord_na_custombroker :name AS custom_broker,
        custrecord_na_containertype :name AS shipped_container_id,
        custrecord_na_containerquantity AS container_qty,
        custrecord_na_bookingvesselvoyage AS booking_vessel_voyage,
        custrecord_na_bookingvesselname AS booking_vessel_name,
        custrecord_na_bookingcontainertype :name AS booking_container_type,
        custrecord_na_bookingcontainerquantity AS booking_container_qty,
        custrecord_acs_container_information,
        to_date(from_utc_timestamp(custrecord_na_estimatedarrivaldate_1stop, 'Australia/Melbourne'), 'yyyy-mm-dd') AS estimated_arrival_date_1stop,
        custrecord_na_lstportoforigin :name AS port_of_origin,
        custrecord_na_lstunloadport :name AS port_of_destination,
        custrecord_na_carrier :name AS carrier,
        custrecord_na_selectvendor :name AS vendor,
        custrecord_na_freighttype :name AS freight_type,
        custrecord_na_20ftqty AS 20ft_qty,
        custrecord_na_40ftqty AS 40ft_qty,
        custrecord_na_20ftfreightrate AS 20ft_freight_rate_usd,
        custrecord_na_40ftfreightrate AS 40ft_freight_rate_usd,
        externaldocumentnumber AS booking_reference_number,
        custrecord_na_freedetention AS free_detention_days,
        custrecord_landed_cost_completed AS shipping_cost_completed,
        custrecord_acs_created_by :name AS shipment_created_by,
        custrecord_na_booked_by :name AS custrecord_na_booked_by

    --noqa: enable=all
    FROM
        (
            SELECT
                shipmentnumber,
                INLINE(
                    FROM_JSON(
                        --noqa: disable=PRS
                        customfieldlist: customfield [*],
                        'array<struct<value:string,scriptId:string>>'
                    )
                --noqa: enable=all
                ) AS (value, scriptid)
            FROM {{ ref("netsuite_inbound_shipment") }}
        )
            PIVOT (
                    MAX(value)
                    FOR scriptid IN (
                        'custrecord_na_vesselname', 'custrecord_na_urgentstatus', 'custrecord_na_totalvolumeincbm', 'custrecord_na_spontime',
                        'custrecord_na_shippedvolume', 'custrecord_na_shippedcontainertype', 'custrecord_na_sealnumber', 'custrecord_na_readydate',
                        'custrecord_na_qcstatus',
                        'custrecord_na_portoforigin',
                        'custrecord_na_portofdestination',
                        'custrecord_na_loadingtype',
                        'custrecord_na_intransitsubstatus',
                        'custrecord_na_inbound_substatus',
                        'custrecord_na_freightrate',
                        'custrecord_na_freightforwarder',
                        'custrecord_na_etdweeknumber',
                        'custrecord_na_etd',
                        'custrecord_na_eta',
                        'custrecord_na_custombroker',
                        'custrecord_na_containertype',
                        'custrecord_na_containerquantity',
                        'custrecord_na_bookingvesselvoyage',
                        'custrecord_na_bookingvesselname',
                        'custrecord_na_bookingcontainertype',
                        'custrecord_na_bookingcontainerquantity',
                        'custrecord_acs_container_information',
                        'custrecord_na_estimatedarrivaldate_1stop',
                        'custrecord_na_lstportoforigin',
                        'custrecord_na_lstunloadport',
                        'custrecord_na_carrier',
                        'custrecord_na_selectvendor',
                        'custrecord_na_freighttype',
                        'custrecord_na_20ftqty',
                        'custrecord_na_40ftqty',
                        'custrecord_na_20ftfreightrate',
                        'custrecord_na_40ftfreightrate',
                        'externaldocumentnumber',
                        'custrecord_na_freedetention',
                        'custrecord_landed_cost_completed',
                        'custrecord_acs_created_by',
                        'custrecord_na_booked_by'
                    )
            )
),

inbship AS (
    SELECT
        shipmentnumber,
        shipmentstatus,
        TO_DATE(FROM_UTC_TIMESTAMP(actualshippingdate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS actualshippingdate,
        TO_DATE(FROM_UTC_TIMESTAMP(actualdeliverydate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS actualdeliverydate,
        TO_DATE(FROM_UTC_TIMESTAMP(expecteddeliverydate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS expecteddeliverydate,
        TO_DATE(FROM_UTC_TIMESTAMP(expectedshippingdate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS expectedshippingdate,
        --, externaldocumentnumber
        --, shipmentmemo
        vesselnumber
    FROM
        {{ ref("netsuite_inbound_shipment") }}
)
,
result AS (
    SELECT
        a.*,
        --noqa: disable=PRS
        b.* EXCEPT(b.custrecord_acs_container_information),
        SPLIT(
            REPLACE(SUBSTRING(b.custrecord_acs_container_information, 2, LENGTH(b.custrecord_acs_container_information) - 2), '},{', '}&{'), '&'
        ) AS container_id
    FROM
        inbship AS a
        LEFT JOIN inbship_customfield AS b ON a.shipmentnumber = b.sn
)
,
handle_container_id AS (
    SELECT
        shipmentnumber,
        EXPLODE(container_id) AS container_information
    FROM result
)
,
extract_all_container_id AS (
    SELECT
        a.shipmentnumber,
        CONCAT_WS(',', ARRAY_SORT(COLLECT_SET(a.container_information:name))) AS container_id
    FROM handle_container_id AS a
    GROUP BY a.shipmentnumber
)

SELECT
    a.shipmentnumber,
    a.shipmentstatus,
    a.actualshippingdate,
    a.actualdeliverydate,
    a.expecteddeliverydate,
    a.expectedshippingdate,
    a.vesselnumber,
    a.vessel_name,
    a.urgent_status,
    a.total_volume_cbm,
    a.spontime,
    a.actual_volum_cbm,
    a.shipped_container_type,
    a.seal_number,
    a.confirmed_ready_date,
    a.qc_status,
    a.contract_port_of_origin,
    a.contract_port_of_destination,
    a.container_loadingtype,
    a.intransit_substatus,
    a.to_be_shipped_substatus,
    a.freight_rate,
    a.freight_forwarder,
    a.etd_week_number,
    a.estimated_departure_date,
    a.estimated_arrival_date,
    a.custom_broker,
    a.shipped_container_id,
    a.container_qty,
    a.booking_vessel_voyage,
    a.booking_vessel_name,
    a.booking_container_type,
    a.booking_container_qty,
    a.estimated_arrival_date_1stop,
    a.port_of_origin,
    a.port_of_destination,
    a.carrier,
    a.vendor,
    a.freight_type,
    a.20ft_qty,
    a.40ft_qty,
    a.20ft_freight_rate_usd,
    a.40ft_freight_rate_usd,
    a.booking_reference_number,
    a.free_detention_days,
    a.shipping_cost_completed,
    a.shipment_created_by,
    a.custrecord_na_booked_by,
    b.container_id
FROM result AS a
    LEFT JOIN extract_all_container_id AS b ON a.shipmentnumber = b.shipmentnumber
