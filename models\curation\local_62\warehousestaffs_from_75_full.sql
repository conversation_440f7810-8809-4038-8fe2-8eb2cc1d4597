{{ config(
    materialized='incremental',
    unique_key = 'unique_key',
    incremental_strategy='merge',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('warehousestaffs_from_75') }}
SELECT
    id,
    role,
    first_name,
    last_name,
    known_as,
    code,
    location,
    job_title,
    company_email,
    warehouse,
    wms_login_id,
{% if is_incremental() %}
    CURRENT_DATE() AS snapshot_date_utc,
    CONCAT(id, '-', CURRENT_DATE()) AS unique_key
FROM {{ ref("warehousestaffs_from_75") }}
{% else %}
    cast({{ input_file_name_substring(97, 10) }} AS date) as snapshot_date_utc,
    concat(id, '-', {{ input_file_name_substring(97, 10) }}) AS unique_key
FROM {{ get_source("62_lake", "warehousestaffs_from_75_full") }}
{% endif %}
