import datetime
import pytz
from pyspark.sql.functions import col, lit, concat

def get_source_path(container, j, year=None, month=None, day=None, is_gcp=False, path_format="date_as_path"):
    if path_format == "latest":
        if is_gcp:
            return f"gs://lake-prod-mr7r/{container}/general_crawlers/{j}/latest.parquet"
        else:
            return f"abfss://{container}@newaimdevadlsaueast.dfs.core.windows.net/general_crawlers/{j}/latest.parquet"
    elif path_format == "ingestion_date":
        if is_gcp:
            return f"gs://lake-prod-mr7r/{container}/general_crawlers/{j}/ingestion_date={year}-{month}-{day}/*/*/*.parquet"
        else:
            return f"abfss://{container}@newaimdevadlsaueast.dfs.core.windows.net/general_crawlers/{j}/ingestion_date={year}-{month}-{day}/*/*/*.parquet"
    elif path_format == "delta":
        if is_gcp:
            return f"gs://lake-prod-mr7r/general_crawlers/ranking_delta/ranking_delta/{j.split('/')[0]}/ingestion_date={year}-{month}-{day}/"
        else:
            return f"abfss://<EMAIL>/ranking_delta/{j.split('/')[0]}/ingestion_date={year}-{month}-{day}/"         
    else:
        assert(year is not None or month is not None or day is not None)
        if is_gcp:
            return f"gs://lake-prod-mr7r/{container}/general_crawlers/{j}/{year}/{month}/{day}/*.parquet"
        else:
            return f"abfss://{container}@newaimdevadlsaueast.dfs.core.windows.net/general_crawlers/{j}/{year}/{month}/{day}/*.parquet"

def model(dbt, session):
    dbt.config(
        materialized = "incremental",
        incremental_strategy = "merge",
        unique_key = "unique_id",
        tags = ["prod", "databricks", "gcp"]
    )

    is_gcp = (dbt.config.get("IS_GCP") == "true")
    if is_gcp:
        container = 'rawzone'
    else:
        container = 'adf-rawzone'

    ranking_crawlers = [
        {
            'crawler_blob_path': 'bigw/ranking',
            'path_formatting_style': 'ingestion_date',
            'refresh_gap_frequency': 1,
        },
        {
            'crawler_blob_path': 'bunnings/ranking',
            'path_formatting_style': 'date_as_path',
            'refresh_gap_frequency': 1,
        },
        {
            'crawler_blob_path': 'ebay/ranking_item',
            'path_formatting_style': 'date_as_path',
            'refresh_gap_frequency': 7,
        },
        {
            'crawler_blob_path': 'mydeal/ranking',
            'path_formatting_style': 'date_as_path',
            'refresh_gap_frequency': 4,
        },
        {
            'crawler_blob_path': 'woolworths/ranking',
            'path_formatting_style': 'ingestion_date',
            'refresh_gap_frequency': 1,
        }
    ]

    tz = pytz.timezone('Australia/Melbourne')
    today = datetime.datetime.now(tz)
    
        
    final_result = None
    for crawler in ranking_crawlers:
        j = crawler['crawler_blob_path']
        path_format_class = crawler['path_formatting_style']
        refresh_frequency = crawler['refresh_gap_frequency']

        if dbt.is_incremental:
            # For incremental, only lookback 3 Refresh Intervals
            days_to_process = [today - datetime.timedelta(days=i) for i in range(refresh_frequency*3)]
        else:
            # For full-refresh, lookback 30 Refresh Intervals
            days_to_process = [today - datetime.timedelta(days=i) for i in range(refresh_frequency*30)]
            
        for day in days_to_process:
            check_day = day.strftime("%Y-%m-%d")
            year = check_day[:4]
            month = check_day[5:7]
            day_str = check_day[8:]
            
            try:
                if check_day < '2025-04-09' or j in ['ebay/ranking_item','mydeal/ranking']:
                    df = session.read.parquet(get_source_path(container, j, year, month, day_str, is_gcp=is_gcp, path_format=path_format_class))
                    count = df.count()
                else:
                    df = session.read.format("delta").load(get_source_path(container, j, year, month, day_str, is_gcp=is_gcp, path_format="delta"))
                    count = df.count()             
               
                result_df = session.createDataFrame(
                    [[j.replace('/', '-').replace('_', '-'), check_day, count, refresh_frequency]], 
                    ["crawler_name", "data_date", "record_count", "refresh_frequency"]
                )
                
                result_df = result_df.withColumn(
                    "unique_id", 
                    concat(col("crawler_name"), lit("-"), col("data_date"))
                )


                if final_result is None:
                    final_result = result_df
                else:
                    final_result = final_result.union(result_df)
                    
            except Exception as e:
                # Skip if no data for this date
                continue

    if final_result is None:
        # Return empty DataFrame with correct schema if no data
        return session.createDataFrame(
            [], 
            ["unique_id", "crawler_name", "data_date", "record_count","refresh_frequency"]
        )

    return final_result.pandas_api()
