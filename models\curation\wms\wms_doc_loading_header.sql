{{ config(
    materialized='incremental',
    unique_key="ldlno",
    incremental_strategy="merge",
    tags = [ "prod", "databricks", "gcp" ]
) }}

-- depends_on: {{ ref('wms_doc_loading_header_raw') }}

{% if is_incremental() %}
    WITH uniq AS (
        SELECT
            status,
            vehicalno,
            vehicletype,
            driver,
            ---- 12&14:rigid, 22:semi,30:B-Double,8:samll truck
            loadingtotime,
            loadingby,
            addtime,
            warehouseid,
            carrierid,
            TRIM(ldlno) AS ldlno,
            CASE
                WHEN vehicletype = 12 OR vehicletype = 14 THEN 'Rigid'
                WHEN vehicletype = 22 THEN 'Semi'
                WHEN vehicletype = 30 THEN 'B-Double'
                WHEN vehicletype = 8 THEN 'Samll truck'
                ELSE 'Van'
            END AS truck_type,
            TO_TIMESTAMP(loadingfromtime) AS loadingfromtime,
            GREATEST(edittime, addtime, FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) AS watermark,
            ROW_NUMBER()
                OVER (
                    PARTITION BY TRIM(ldlno) ORDER BY GREATEST(edittime, addtime, FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) DESC
                )
            AS row_id
        FROM
            {{ ref("wms_doc_loading_header_raw") }}
        WHERE
            GREATEST(edittime, addtime, FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) >= DATE_SUB(CURRENT_DATE(), 5)
    )

    SELECT *
    FROM
        uniq
    WHERE
        row_id = 1
{% else %}
WITH unioned AS (
    SELECT
        TRIM(ldlno) AS ldlno,
        status,
        vehicalno,
        vehicletype,
        driver,
        loadingfromtime,
        loadingtotime,
        loadingby,
        addtime,
        edittime,
        carrierid,
        warehouseid,
        FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne') AS ingestion_time_aest
    FROM
        {{ get_source("lake","doc_loading_header_recent_history") }}
    UNION
        SELECT
        TRIM(ldlno) AS ldlno,
        status,
        vehicalno,
        vehicletype,
        driver,
        loadingfromtime,
        loadingtotime,
        loadingby,
        addtime,
        edittime,
        carrierid,
        warehouseid,
        FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne') AS intestion_time_aest
    FROM
        {{ get_source("lake","doc_loading_header_history") }}
    UNION
    SELECT
        trim(ldlno) AS ldlno,
        status,
        vehicalno,
        vehicletype,
        driver,
        loadingfromtime,
        loadingtotime,
        loadingby,
        addtime,
        edittime,
        carrierid,
        warehouseid,
        FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne') AS ingestion_time_aest
    FROM
        {{ get_source("lake","doc_loading_header_partition") }}
),
uniq AS(
    SELECT
        TRIM(ldlno) AS ldlno,
        status,
        vehicalno,
        vehicletype,
        ---- 12&14:rigid, 22:semi,30:B-Double,8:samll truck
        CASE
            WHEN vehicletype = 12 OR vehicletype = 14 THEN 'Rigid'
            WHEN vehicletype = 22 THEN 'Semi'
            WHEN vehicletype = 30 THEN 'B-Double'
            WHEN vehicletype = 8 THEN 'Samll truck'
            ELSE 'Van' END AS truck_type,
        driver,
        to_timestamp(loadingfromtime) AS loadingfromtime,
        loadingtotime,
        loadingby,
        addtime,
        warehouseid,
        carrierid,
        GREATEST(edittime, addtime, ingestion_time_aest) AS watermark,
        ROW_NUMBER() OVER (PARTITION BY TRIM(ldlno) ORDER BY GREATEST(edittime, addtime, ingestion_time_aest)  DESC) AS row_id
FROM
    unioned
)
SELECT
    *
FROM
    uniq
WHERE
    row_id = 1
{% endif %}
