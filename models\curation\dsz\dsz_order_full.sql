{{ config(
    materialized='incremental',
    unique_key="increment_id",
    incremental_strategy="merge", 
    on_schema_change='append_new_columns',
    partition_by = 'order_year',
    tags = [ "prod", "databricks","full_refresh", "gcp" ]
) }}
WITH unic AS (
    SELECT
        customer_email,
        customer_id,
        FROM_UTC_TIMESTAMP(dispatch_time, 'Australia/Melbourne') AS dispatch_time_aest,
        items,
        payment_way,
        remark,
        serial_number,
        shipment,
        shipping_address,
        status,
        txn_id,
        capture_at AS ingestion_at_utc,
        FROM_UTC_TIMESTAMP(payment_date, 'Australia/Melbourne') AS payment_date_aest,
        po,
        CAST(increment_id AS STRING) AS increment_id,
        FROM_UTC_TIMESTAMP(created_at, 'Australia/Melbourne') AS created_at_aest, --Order Datetime For Megento
        CAST(grand_total AS DOUBLE) AS grand_total,
        FROM_UTC_TIMESTAMP(FROM_UNIXTIME(ordered_timestamp), 'Australia/Melbourne') AS order_datetime_aest,
        CAST(shipping_fee AS DOUBLE) AS shipping_fee,
        COALESCE(updated_at, created_at) AS updated_at_utc,
        processing_fee,
        ROW_NUMBER() OVER (PARTITION BY increment_id ORDER BY COALESCE(capture_at, updated_at, created_at) DESC) AS row_id
    FROM
        {{ get_source("lake", "dsz_order_full") }}
    {% if is_incremental() %}
        WHERE
            COALESCE(updated_at, created_at) >= (SELECT DATE_SUB(MAX(updated_at_utc), 7) FROM {{ this }})
    {% endif %}
)


SELECT
    increment_id,
    created_at_aest,
    customer_email,
    customer_id,
    dispatch_time_aest,
    grand_total,
    items,
    order_datetime_aest,
    payment_way,
    remark,
    serial_number,
    shipment,
    shipping_address,
    shipping_fee,
    status,
    txn_id,
    po,
    processing_fee,
    updated_at_utc,
    ingestion_at_utc,
    payment_date_aest,
    YEAR(created_at_aest) AS order_year
FROM
    unic
WHERE
    row_id = 1
    AND increment_id IS NOT NULL
