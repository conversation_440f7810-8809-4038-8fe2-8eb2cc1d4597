{{ config(
    materialized = 'incremental',
    partition_by = "ingestion_date",
    incremental_strategy= "insert_overwrite",
    file_format='delta',
    tags = [ "prod", "databricks" , "gcp"]
) }}
SELECT DISTINCT
    product_link,
    price,
    rrp,
    discount,
    product_title,
    product_id,
    ranking,
    CASE WHEN page > 1 THEN 55 + (page - 2) * 48 + ranking ELSE ranking END AS overall_ranking,
    rating,
    reviews,
    page,
    keyword,
    free_shipping,
    limited_free_shipping,
    seller_name,
    brand_name,
    capture_at,
    product_sku,
    response_status,
    CAST(capture_at AS DATE) AS ingestion_date
{% if is_incremental() %}
FROM
    {{ get_source("lake", "mydeal_ranking") }}
WHERE capture_at IS NOT NULL AND product_sku IS NOT NULL
{% else %}
FROM
    {{ get_source("lake", "mydeal_ranking_full") }}
    WHERE capture_at IS NOT NUll AND product_sku IS NOT NUll
        {% if env_var('DBT_ENV', 'dev') == 'dev' %}
            AND capture_at >= DATE_SUB(current_date(), 20)
        {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
            AND capture_at >= DATE_SUB(current_date(), 60)
        {% endif %}
{% endif %}
