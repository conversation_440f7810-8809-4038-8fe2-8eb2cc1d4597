{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT DISTINCT
    product_link,
    price,
    rrp,
    discount,
    product_title,
    product_id,
    ranking,
    CASE WHEN page > 1 THEN 55 + (page - 2) * 48 + ranking ELSE ranking END AS overall_ranking,
    rating,
    reviews,
    page,
    keyword,
    free_shipping,
    limited_free_shipping,
    seller_name,
    brand_name,
    capture_at,
    product_sku,
    response_status,
    ingestion_time_utc,
    TO_DATE(FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) AS ingestion_date
FROM {{ get_source("lake", "mydeal_ranking") }}
WHERE capture_at IS NOT NULL AND product_sku IS NOT NULL
