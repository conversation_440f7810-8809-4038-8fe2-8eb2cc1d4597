{{ config(
        materialized = "table",
        tags = [ "prod", "gcp", "databricks" ]
) }}

-- depends on: {{ ref ("dsz_sku_google_result") }}


WITH rank_by_date AS (
    SELECT
        id,
        sku,
        title,
        link,
        web_title,
        image,
        rank,
        search_by_sku,
        capture_date,
        RANK() OVER (PARTITION BY sku ORDER BY capture_date DESC) AS rank_num
    FROM {{ ref ("dsz_sku_google_result") }}
    WHERE search_by_sku IS TRUE
),

lastest_result AS (SELECT * EXCEPT (rank_num) FROM rank_by_date WHERE rank_num = 1 AND link IS NOT NULL)

SELECT l.*
FROM lastest_result AS l LEFT JOIN {{ ref ('dsz_product') }} AS d ON l.sku = d.sku
WHERE d.active_status IS TRUE AND d.vendor_product IS TRUE
