{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        bunnings_id,
        order_id,
        f_responsible AS user_id,
        from_id,
        IF(from_type IN ('CUSTOMER', 'OPERATOR'), 'customer', 'admin') AS role_, --2014
        FROM_UTC_TIMESTAMP(date_created, 'Australia/Melbourne') AS created_aest,
        FROM_UTC_TIMESTAMP(COALESCE(replied_date, date_created), 'Australia/Melbourne') AS watermark,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(replied_date, date_created) DESC) AS row_id
    FROM
        {{ get_source('lake','dl_bunnings_message') }}
)

SELECT *
FROM
    uniq
WHERE
    row_id = 1
