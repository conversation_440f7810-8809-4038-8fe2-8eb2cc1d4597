{{ config(
    materialized = 'table',
    unique_key = 'primary_key',
    file_format='delta',
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}

--SMG Process Log considers of 4 Actions: add, submit, open and sent only
-- add -> submit : must have ticket_id
-- open -> sent
{% if is_incremental() %}
    WITH uniq AS (
        SELECT
            CONCAT(
                f_user_id,
                '-',
                channel_id,
                '-',
                COALESCE(f_message_id, 'MMMM'),
                '-',
                COALESCE(f_ticket_id, 'TTTT'),
                '-',
                f_action,
                '_',
                f_type
            ) AS primary_key,
            f_user_id AS user_id,
            channel_id AS channel,
            f_message_id AS message_id,
            f_ticket_id AS ticket_id,
            f_datetime AS datetime_aest, --report_time
            f_action,
            ROW_NUMBER() OVER (PARTITION BY CONCAT(
                f_user_id,
                '-',
                channel_id,
                '-',
                COALESCE(f_message_id, 'MMMM'),
                '-',
                COALESCE(f_ticket_id, 'TTTT'),
                '-',
                f_action,
                '_',
                f_type
            ) ORDER BY f_datetime DESC) AS row_no
        FROM
            {{ get_source('lake','dl_process_timelog') }}
        WHERE
            f_datetime IS NOT NULL
            AND (f_ticket_id IS NOT NULL AND f_action IN ('add', 'submit'))
            AND f_action IN ('add', 'submit', 'open', 'sent')
            AND f_datetime >= (SELECT MAX(datetime_aest) FROM {{ this }})
    )

    SELECT *
    FROM
        uniq
    WHERE
        row_no = 1
{% else %}
WITH 
unioned AS(
    SELECT
        f_user_id, 
        channel_id, 
        f_message_id,
        f_ticket_id,
        f_datetime, 
        f_action,
        f_type
    FROM
        {{ get_source('lake','dl_process_timelog_history') }}
    UNION
    SELECT
        f_user_id, 
        channel_id, 
        f_message_id,
        f_ticket_id,
        f_datetime, 
        f_action,
        f_type
    FROM
        {{ get_source('lake','dl_process_timelog_partition') }}
),
uniq AS(
    SELECT
        CONCAT(
            f_user_id,
            '-',
            channel_id,
            '-',
            IFNULL(f_message_id, 'MMMM'),
            '-',
            IFNULL(f_ticket_id, 'TTTT'),
            '-',
            f_action,
            '_',
            f_type
        ) AS primary_key,
        f_user_id AS user_id,
        channel_id AS channel,
        f_message_id AS message_id,
        f_ticket_id AS ticket_id,
        f_datetime AS datetime_aest, --report_time
        f_action,
        ROW_NUMBER() OVER (PARTITION BY CONCAT(
            f_user_id,
            '-',
            channel_id,
            '-',
            IFNULL(f_message_id, 'MMMM'),
            '-',
            IFNULL(f_ticket_id, 'TTTT'),
            '-',
            f_action,
            '_',
            f_type
            ) ORDER BY f_datetime DESC) AS row_no
    FROM
        unioned
    WHERE
        f_datetime IS NOT NULL 
        AND (f_ticket_id IS NOT NULL AND f_action IN ('add', 'submit'))
        AND f_action IN ('add', 'submit', 'open', 'sent')
)
SELECT
    *
FROM
    uniq
WHERE
    row_no = 1
{% endif %}
