{{ config(
    materialized='table',
) }}

/*
This table aggregates SLA data for SKU tracking crawlers.
This query performs several checks on the SLA data for SKU tracking crawlers.
It checks the following:
1. The latest failed percentage should not exceed 'Mean + 3 * std' of the past 30 executions.
2. The latest success percentage should not be below 'Mean - 3 * std' of the past 30 executions.
3. The latest total requests should not be below 'Mean - 3 * std' of the past 30 executions.
4. The latest total success should not be below 'Mean - 3 * std' of the past 30 executions.
5. The latest data should be within the past 10 days.

High-level design:
1. recent_sku_tracking_crawlers_sla: Fetches the recent SLA data for SKU tracking crawlers, limited to the past 8 months.
2. past_30_runs_aggregate: Aggregates the SLA data for the past 30 runs, calculating mean and standard deviation for various metrics.
3. latest_sla: Joins the recent SLA data with the aggregated data to get the latest SLA metrics along with the aggregated statistics.
4. Final SELECT statements:
    Performs the checks and outputs the results, including the department, target, item, check result, check status, and updated timestamp.
*/

WITH recent_sku_tracking_crawlers_sla AS (
    SELECT
        ingestion_date,
        channel,
        total_requests,
        total_success,
        failed_percentage,
        success_percentage,
        ROW_NUMBER() OVER (PARTITION BY channel ORDER BY ingestion_date DESC) AS rn
    FROM
        {{ ref("sku_tracking_crawlers_sla") }}
    WHERE
        ingestion_date >= DATEADD(MONTH, -8, CURRENT_DATE)
),

past_30_runs_aggregate AS (
    SELECT
        channel,
        AVG(failed_percentage) AS mean_30_failed_percentage,
        AVG(success_percentage) AS mean_30_success_percentage,
        STDDEV(failed_percentage) AS std_dev_30_failed_percentage,
        STDDEV(success_percentage) AS std_dev_30_success_percentage,
        AVG(total_requests) AS mean_30_total_requests,
        AVG(total_success) AS mean_30_total_success,
        STDDEV(total_requests) AS std_dev_30_total_requests,
        STDDEV(total_success) AS std_dev_30_total_success,
        MIN(ingestion_date) AS analysis_start_date,
        MAX(ingestion_date) AS analysis_end_date,
        COUNT(*) AS num_runs_involved
    FROM
        recent_sku_tracking_crawlers_sla
    WHERE
        rn <= 30
    GROUP BY
        channel
),

-- Latest SLA data
latest_sla AS (
    SELECT
        a.ingestion_date,
        a.channel,
        a.total_requests,
        a.total_success,
        a.failed_percentage,
        a.success_percentage,
        b.mean_30_failed_percentage,
        b.mean_30_success_percentage,
        b.std_dev_30_failed_percentage,
        b.std_dev_30_success_percentage,
        b.mean_30_total_requests,
        b.mean_30_total_success,
        b.std_dev_30_total_requests,
        b.std_dev_30_total_success,
        b.analysis_start_date,
        b.analysis_end_date,
        b.num_runs_involved
    FROM
        recent_sku_tracking_crawlers_sla AS a
        LEFT JOIN past_30_runs_aggregate AS b
            ON a.channel = b.channel
    WHERE
        a.rn = 1
)


SELECT
    'data' AS department,
    'channel-health-check' AS target,
    CONCAT(
        channel,
        " Sufficiency check: The latest channel health check failed percentage should 
        not exceed 'Mean + Std * 3' of the past 30 executions"
    ) AS item,
    CAST(failed_percentage - mean_30_failed_percentage - 3 * std_dev_30_failed_percentage AS STRING) AS check_result,
    CASE
        WHEN
            failed_percentage - mean_30_failed_percentage - 3 * std_dev_30_failed_percentage <= 0
            THEN 'PASS'
        ELSE 'ERROR'
    END AS check_status,
    TO_TIMESTAMP(ingestion_date, 'yyyy-MM-dd') AS updated_at_aueast
FROM
    latest_sla
UNION
SELECT
    'data' AS department,
    'channel-health-check' AS target,
    CONCAT(
        channel,
        " Sufficiency check: The latest channel health check success percentage should 
        not below 'Mean - Std * 3' of the past 30 executions"
    ) AS item,
    CAST(success_percentage - mean_30_success_percentage + 3 * std_dev_30_success_percentage AS STRING) AS check_result,
    CASE
        WHEN
            success_percentage - mean_30_success_percentage + 3 * std_dev_30_success_percentage >= 0
            THEN 'PASS'
        ELSE 'ERROR'
    END AS check_status,
    TO_TIMESTAMP(ingestion_date, 'yyyy-MM-dd') AS updated_at_aueast
FROM
    latest_sla
UNION
SELECT
    'data' AS department,
    'channel-health-check' AS target,
    CONCAT(
        channel,
        " Sufficiency check: The latest channel health check total requests should 
        not below 'Mean - Std * 3' of the past 30 executions"
    ) AS item,
    CAST(total_requests - mean_30_total_requests + 3 * std_dev_30_total_requests AS STRING) AS check_result,
    CASE
        WHEN
            total_requests - mean_30_total_requests + 3 * std_dev_30_total_requests >= 0
            THEN 'PASS'
        ELSE 'ERROR'
    END AS check_status,
    TO_TIMESTAMP(ingestion_date, 'yyyy-MM-dd') AS updated_at_aueast
FROM
    latest_sla
UNION
SELECT
    'data' AS department,
    'channel-health-check' AS target,
    CONCAT(
        channel,
        " Sufficiency check: The latest channel health check total success should 
        not below 'Mean - Std * 3' of the past 30 executions"
    ) AS item,
    CAST(total_success - mean_30_total_success + 3 * std_dev_30_total_success AS STRING) AS check_result,
    CASE
        WHEN total_success - mean_30_total_success + 3 * std_dev_30_total_success >= 0
            THEN 'PASS'
        ELSE 'ERROR'
    END AS check_status,
    TO_TIMESTAMP(ingestion_date, 'yyyy-MM-dd') AS updated_at_aueast
FROM
    latest_sla
UNION
SELECT
    'data' AS department,
    'channel-health-check' AS target,
    CONCAT(channel, ' Freshness check: The latest channel health check data is within the past 10 days') AS item,
    CAST(DATEDIFF(CURRENT_DATE, analysis_end_date) AS STRING) AS check_result,
    CASE WHEN DATEDIFF(CURRENT_DATE, analysis_end_date) <= 10 THEN 'PASS' ELSE 'ERROR' END AS check_status,
    TO_TIMESTAMP(ingestion_date, 'yyyy-MM-dd') AS updated_at_aueast
FROM
    latest_sla
