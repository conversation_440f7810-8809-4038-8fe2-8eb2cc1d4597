{{ config(
    materialized = 'incremental',
    file_format = 'delta',
    incremental_strategy='merge',
    unique_key = 'uniq_key',
    parition_by = 'modified_year',
    on_schema_change='append_new_columns',
    pre_hook = "
    {%- set target_relation = adapter.get_relation(
      database=this.database,
      schema=this.schema,
      identifier=this.name) -%}
    {%- set table_exists=target_relation is not none -%}
    {%- if table_exists -%}
                DELETE FROM 
                    {{ this }}
                WHERE
                    modified_year >= YEAR(CURRENT_DATE()) - 1
                AND
                    modified >=
                    (SELECT MIN(modified) FROM {{ref('dl_ticket_sku_30days')}});
    {%- endif -%}",
    tags = [ "prod", "databricks", "gcp" ]
) }}

-- depends_on: {{ ref("dl_ticket") }}
-- depends_on: {{ ref("dl_ticket_sku") }}
-- depends_on: {{ ref('dl_ticket_raw') }}
-- depends_on: {{ ref('dl_ticket_sku_30days') }}


--No incremenatal , id will be missing from source data 
WITH oms AS (
    SELECT
        order_no,
        sku,
        store,
        MAX(job_no) AS job_no
    FROM
        {{ ref('oms_sales') }}
    WHERE
        order_no IN (SELECT f_oms_order_id FROM {{ ref('dl_ticket') }})
    GROUP BY
        order_no,
        sku,
        store
)
{% if is_incremental() %}
    --noqa: disable=LT04
    , unic AS (
        SELECT
            id,
            f_handle_status,
            f_add_time,
            f_order_at,
            f_order_sent,
            f_oms_order_id,
            f_ticket_no,
            f_sku,
            f_order_status,
            f_tracking_number,
            f_handle_mode,
            f_title,
            f_description,
            f_refund_at,
            f_resend_at,
            f_refund_amount,
            f_resend_amount,
            f_department,
            CAST(
                COALESCE(
                    f_last_updatetime,
                    f_add_time
                ) AS TIMESTAMP
            ) AS modified,
            ROW_NUMBER() OVER (PARTITION BY id ORDER BY CAST(
                COALESCE(
                    f_last_updatetime,
                    f_add_time
                ) AS TIMESTAMP
            ) DESC) AS row_no
        FROM
            {{ ref('dl_ticket_raw') }}
        WHERE
            f_oms_order_id IS NOT NULL
            AND
            f_oms_order_id NOT IN ('', '/', '123456789')
    ),

    dl_ticket AS (
        SELECT
            * EXCEPT (row_no),
            YEAR(modified) AS modified_year
        FROM
            unic
        WHERE
            row_no = 1
            AND
            modified >= (SELECT MIN(modified) FROM {{ ref('dl_ticket_sku_30days') }})
    )
{% endif %}

SELECT
    ts.uniq_key,
    COALESCE(t.f_handle_status, 'No Status') AS handle_status,
    CAST(t.f_add_time AS TIMESTAMP) AS created_at,
    CAST(t.f_order_at AS Date) AS order_date,
    t.f_order_sent AS order_sent,
    t.f_oms_order_id AS order_no,
    t.f_ticket_no AS ticket_no,
    UPPER(TRIM(ts.f_sku)) AS sku,
    t.f_sku AS listing_sku,
    CASE WHEN COALESCE(ts.f_job_no, '') = '' THEN a.job_no ELSE COALESCE(ts.f_job_no, '') END AS job_no,
    COALESCE(t.f_order_status, 'No Status') AS order_status,

    --some store are missing in dl_ticket table -- left join ids order_status for store 
    --t.f_shop_name AS store,

    a.store AS store,
    ts.f_problem_reason AS problem_reason,
    t.f_tracking_number AS return_tracking_number,
    t.f_handle_mode AS refund_method,
    t.f_title AS title,
    t.f_description AS faulty_description,
    CAST(COALESCE(t.f_refund_at, t.f_resend_at) AS DATE) AS refund_resend_at,
    IF(
        COALESCE(t.f_refund_amount, '') = '',
        0.0,
        CAST(t.f_refund_amount AS FLOAT)
    ) AS refund_amount,
    IF(
        COALESCE(t.f_resend_amount, '') = '',
        0.0,
        CAST(t.f_resend_amount AS FLOAT)
    ) AS resend_amount,
    ts.f_big_level AS problem_lvl_1,
    ts.f_second_level AS problem_lvl_2,
    ts.f_problem_reason AS problem_lvl_3,
    ts.f_ordered_qty AS ordered_qty,

    --some unit_cost show null value, this column is not reliable--
    --ts.f_unit_cost AS unit_Cost,
    t.f_department AS department,
    COALESCE(ts.f_unit_cost, 0) AS unit_cost,
    t.modified,
    t.modified_year,
    CASE
        WHEN COALESCE(ts.f_has_problem, 1) = 0
            THEN 1
        ELSE COALESCE(ts.f_has_problem, 1)
    END AS problem_qty
FROM
    {% if is_incremental() %}
        dl_ticket AS t
    {% else %}
    {{ ref('dl_ticket') }} AS t
    {% endif %}

    INNER JOIN
        {{ ref('dl_ticket_sku') }} AS ts
        ON
            t.id = ts.f_ticketid
    LEFT JOIN
        oms AS a
        ON
            t.f_oms_order_id = a.order_no AND UPPER(TRIM(ts.f_sku)) = a.sku
