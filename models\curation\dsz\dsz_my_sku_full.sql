{{ config(
    materialized = "incremental",
    unique_key = "unique_key",
    incremental_strategy = "merge",
    merge_exclude_columns = ["ingestion_timestamp", "ingestion_date"],
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('dsz_my_sku') }}
{% if is_incremental() %}
    WITH history AS (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY retailer_id, sku ORDER BY ingestion_date DESC) AS row_id
        FROM {{ ref("dsz_my_sku") }}
    )

    SELECT
        retailer_id,
        sku,
        CONCAT(retailer_id, '-', sku) AS unique_key,
        ingestion_timestamp,
        ingestion_date
    FROM history
    WHERE row_id = 1
{% else %}
WITH history AS (
    SELECT
        retailer_id,
        sku,
        ingestion_timestamp,
        ingestion_date,
        concat(retailer_id, '-', sku) AS unique_key,
        lag(ingestion_timestamp) OVER(PARTITION BY retailer_id, sku ORDER BY ingestion_timestamp ASC)
        AS last_ingestion_timestamp
    FROM {{ get_source("lake","dsz_my_sku") }}
)
SELECT retailer_id,
       sku,
       unique_key,
       ingestion_timestamp,
       ingestion_date
FROM history
WHERE last_ingestion_timestamp IS NULL
{% endif %}
