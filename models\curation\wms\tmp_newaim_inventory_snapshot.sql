{{ config(
    materialized='incremental',
    unique_key = 'primary_key',
    incremental_strategy="merge", 
    partition_by = 'snapshot_year',
    tags = [ "prod", "databricks", "gcp" ]
) }}
{% if is_incremental() %}
    SELECT
        CONCAT(
            snapshotdate,
            '-',
            UPPER(TRIM(sku)),
            '-',
            COALESCE(TO_DATE(receivingdate), TO_DATE(snapshotdate)),
            '-',
            COALESCE(cost, ''),
            '-',
            warehouseid
        ) AS primary_key,
        YEAR(snapshotdate) AS snapshot_year,
        TO_DATE(snapshotdate) AS snapshotdate,
        UPPER(TRIM(sku)) AS sku,
        COALESCE(TO_DATE(receivingdate), TO_DATE(snapshotdate)) AS receivingdate,
        cost,
        CAST(SUM(qty) AS INT) AS qty,
        SUM(qty) * cost AS amount,
        warehouseid
    FROM
        {{
        get_source(
            "lake",
            "TMP_NEWAIM_INVENTORY_SNAPSHOT"
        )
    }}
    GROUP BY
        snapshotdate,
        UPPER(TRIM(sku)),
        COALESCE(TO_DATE(receivingdate), TO_DATE(snapshotdate)),
        cost,
        warehouseid

{% else %}
WITH union_ AS(
  SELECT
    snapshotdate,
    sku,
    receivingdate,
    cost,
    qty,
    warehouseid,
    ingestion_time_utc
  FROM
        {{ get_source("lake", "TMP_NEWAIM_INVENTORY_SNAPSHOT_partition") }}
  WHERE sku != 'FOUNT-3LVL-BK' AND receivingdate != ''
  UNION
  SELECT
    snapshotdate,
    sku,
    receivingdate,
    cost,
    qty,
    warehouseid,
    ingestion_time_utc
  FROM
        {{ get_source("lake", "TMP_NEWAIM_INVENTORY_SNAPSHOT_history") }}
  WHERE sku != 'FOUNT-3LVL-BK' AND receivingdate != ''
),
uniq AS(
    SELECT
        snapshotdate,
        sku,
        IFNULL(receivingdate, '') AS receivingdate,
        IFNULL(cost, 0) cost,
        qty,
        warehouseid,
        ingestion_time_utc,
        RANK() OVER(
            PARTITION BY
              snapshotdate,
              sku,
              IFNULL(receivingdate, ''),
              IFNULL(cost, 0),
              qty,
              warehouseid
            ORDER BY
            TO_DATE(ingestion_time_utc),
            TO_DATE(snapshotdate) DESC
        ) AS row_id
  FROM
    union_
)
SELECT
    CONCAT(
        snapshotdate,
        '-',
        UPPER(TRIM(sku)),
        '-',
        IFNULL(TO_DATE(receivingdate), TO_DATE(snapshotdate)),
        '-',
        IFNULL(cost, ''),
        '-',
        warehouseid
    ) AS primary_key,
    YEAR(snapshotdate) AS snapshot_year,
    TO_DATE(snapshotdate) AS snapshotdate,
    UPPER(TRIM(sku)) AS sku,
    IFNULL(TO_DATE(receivingdate), TO_DATE(snapshotdate)) AS receivingdate,
    cost,
    CAST(SUM(qty) AS INT) AS qty,
    SUM(qty) * Cost AS amount,
    warehouseid
  FROM
    uniq
  WHERE
    row_id = 1
  GROUP BY
    snapshotdate,
    UPPER(TRIM(sku)),
    IFNULL(TO_DATE(receivingdate), TO_DATE(snapshotdate)),
    cost,
    warehouseid
{% endif %}
