{{ config(
    materialized='incremental',
    unique_key = 'sku',
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH latest_v1_table AS (
    SELECT
        sku,
        sku_class,
        act,
        nsw_m,
        nsw_r,
        nt_m,
        nt_r,
        qld_m,
        qld_r,
        remote,
        sa_m,
        sa_r,
        tas_m,
        tas_r,
        vic_m,
        vic_r,
        wa_m,
        wa_r,
        generate_date
    FROM
        {{ ref("dsol_rate_table_incl_gst") }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY sku ORDER BY generate_date DESC) = 1
),

latest_v2_table AS (
    SELECT
        listing_sku AS sku,
        NULL AS sku_class,
        act,
        nsw_m,
        nsw_r,
        nt_m,
        nt_r,
        qld_m,
        qld_r,
        remote,
        sa_m,
        sa_r,
        tas_m,
        tas_r,
        vic_m,
        vic_r,
        wa_m,
        wa_r,
        generate_date
    FROM
        {{ ref("dsol_rate_table_incl_gst_v2") }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY listing_sku ORDER BY generate_date DESC) = 1
),

union_tables AS (
    SELECT
        sku,
        sku_class,
        act,
        nsw_m,
        nsw_r,
        nt_m,
        nt_r,
        qld_m,
        qld_r,
        remote,
        sa_m,
        sa_r,
        tas_m,
        tas_r,
        vic_m,
        vic_r,
        wa_m,
        wa_r,
        generate_date,
        'v2' AS source_table
    FROM
        latest_v2_table
    UNION ALL
    SELECT
        a.sku,
        a.sku_class,
        a.act,
        a.nsw_m,
        a.nsw_r,
        a.nt_m,
        a.nt_r,
        a.qld_m,
        a.qld_r,
        a.remote,
        a.sa_m,
        a.sa_r,
        a.tas_m,
        a.tas_r,
        a.vic_m,
        a.vic_r,
        a.wa_m,
        a.wa_r,
        a.generate_date,
        'v1' AS source_table
    FROM
        latest_v1_table AS a
        LEFT JOIN
            latest_v2_table AS b ON a.sku = b.sku
    WHERE
        b.sku IS NULL
),

ranked_data AS (
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY sku ORDER BY
                CASE
                    WHEN source_table = 'v2' THEN 1 -- Prioritize v2 Table
                    ELSE 2
                END,
                generate_date DESC -- Then latest date in case of ties (though with our UNION ALL logic, this should mostly handle itself)
        ) AS rn
    FROM
        union_tables
)

SELECT
    sku,
    sku_class,
    act,
    nsw_m,
    nsw_r,
    nt_m,
    nt_r,
    qld_m,
    qld_r,
    remote,
    sa_m,
    sa_r,
    tas_m,
    tas_r,
    vic_m,
    vic_r,
    wa_m,
    wa_r,
    generate_date
FROM
    ranked_data
WHERE
    rn = 1
    {% if is_incremental() %}
    -- This ensures we only process new or updated records in incremental runs
    -- We need to re-evaluate the max generate_date based on the combined logic
        AND
        generate_date > (SELECT COALESCE(MAX(generate_date), '1900-01-01') FROM {{ this }})
    {% endif %}
