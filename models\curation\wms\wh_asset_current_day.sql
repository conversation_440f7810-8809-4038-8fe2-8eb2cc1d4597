{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    asset_no,
    serial_number,
    location,
    make,
    type_of_equipment,
    status,
    notes,
    ingestion_date
FROM {{ ref("wh_asset_dc01") }}
UNION
SELECT
    asset_no,
    serial_number,
    location,
    make,
    type_of_equipment,
    status,
    notes,
    ingestion_date
FROM {{ ref("wh_asset_wh06") }}
UNION
SELECT
    asset_no,
    serial_number,
    location,
    make,
    type_of_equipment,
    status,
    notes,
    ingestion_date
FROM {{ ref("wh_asset_wh08") }}
UNION
SELECT
    asset_no,
    serial_number,
    location,
    make,
    type_of_equipment,
    status,
    notes,
    ingestion_date
FROM {{ ref("wh_asset_wh09") }}
