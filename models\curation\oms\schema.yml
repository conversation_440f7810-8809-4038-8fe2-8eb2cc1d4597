version: 2

models:
  - name: order_allocation_detail_unioned
    description: |
      On of six foundations for oms sales fact table, this table record sku package allocation detail, the source table is not stable
      A lost of Missing data, if package sku in oms sales data is null , that is becasue some data are missing in this table,
      order_allocation_detail_unioned= order_allocation_detail_history + order_allocation_detail
    columns:
      - name: modified
        description: |
          latest modifed time
        tests:
          - not_null
      - name: row_no
        description: A flag for decuplication process. All the data is selected given row no=1 order by latest modified
        tests:
          - not_null
  - name: order_detail_cust_unioned
    description: |
      One of six fundations for oms sales fact table, order detail_cust_full =order_detail_cust_history +order_detail_cust
      This table records all the information about postage cost and charge.
      This table is not stabilised and has a lot of missing data. If the initial postage estimation or postage estimation is null, that is because of left join failure due to missing data in the right side table
    columns:
      - name: modified
        description: |
          The latest modified time
        tests:
          - not_null
      - name: id
        description: |
          Primary key
        tests: 
          - not_null
  - name: order_detail_unioned
    description: |
      One of six foundations for oms saels fact table,This table records all the SKU information per order no; the most crucial table, which populates all the profit margin, sales, cogs, and profit margin for all the end users. 
      order detail full = order detail history + order detail
    columns:
      - name: sku
        description: |
          base sku 
        tests:
          - not_null
      - name: id
        description: |
          Primary key
        tests:  
          - not_null
          - unique
      - name: modified
        description: |
          the latest modifed time
        tests:
          - not_null
  - name: order_header_unioned
    description: |
      one of six fundations for oms sales fact. This table records customer information and order no should be unique,
      one of the key information is order time and change_order_status
    columns:
      - name: order_no
        description: |
          The order_no after split, if the order is not splitted, it will be the same as the original order_no
        tests:
          - not_null
      - name: order_time
        description: |
          The time when a order placed in the system
        tests: 
          - not_null
      - name: change order_status
        description: |
          A Flag for swap order , when status=1 then swap order = Yes
      - name: province
        description: |
          Australian State or New Zealand State for customer address
        tests:
          - not_null
      - name: split_order_no
        description: |
          The original order_no before split
        tests: 
          - not_null
      - name: split_order_status
        description: |
          Whether the original order is splitted or not, 1 for splitted, 0 for not splitted
        tests: 
          - not_null
  - name: order_status_unioned
    description: |
      one of six fundations for oms sales fact table. This table records order status
    columns:
      - name: shop_id
        description: |
          Store or Channel Name in OMS
        tests:
          - not_null
      - name: express_code
        description: |
          The way a sku is delivered to the customer
        tests:
          - not_null
      - name: order_status
        description:
          The status for each order no, 60 = cancel
        tests:
          - not_null
  - name: order_header_cust_unioned
    description: |
      One of six fundations for oms_sales fact table. The information is used from this table is delivered in oms sales
    columns:
      - name: modified
        description: |
          The latest modified time
        tests:
          - not_null
      - name: order_no
        description: |
          Primary key
        tests:
          - not_null
          - unique
      - name: customer_order_id
        description: customer_order_id    
  - name: order_detail_update_unioned
    description: |
      A table to record the update action for the sales orders. "created" time will not be updated
    columns:
      - name: id
        description: |
          unique key
        tests:
          - not_null
          - unique
      - name: order_no
        description: |
          order number
        tests:
          - not_null
      - name: action
        description: update reason
        tests:
          - not_null
      - name: created
        description: order update time (log created time)
        tests:
          - not_null
  - name: base_sku_revenue_90days
    description: This is the fact table of OMS in base sku level only contains the latest 90 days data 
    columns:
      - name: id
        tests:
          - not_null
          - unique
      - name: tid
        tests:
          - not_null:
              config:
                severity: error
                error_if: ">100"
                warn_if: ">1"
      - name: order_no
        tests:
          - not_null:
              config:
                severity: error
                error_if: ">100"
                warn_if: ">1"
      - name: sku
        tests:
          - not_null
      - name: order_time
        test:
          - not_null
      - name: store
        test:
          - not_null
      - name: is_swap_order
        description: |
          IF an sku is a swap item , Y = Swap Order , N = Normal Order
        test:
          - not_null
      - name: qty
        test:
          - not_null
      - name: postage_estimation
        description: |
          source from order_header_cust table
      - name: state_
        test:
          - not_null
      - name: postage_estimation_map
        description: |
          "Key -> value" format for valid postage estimation column
      - name: subtotal
        description: SUM os sku sales price at that point of time including GST
        tests:
          - not_null
      - name: sales_ex_gst
        description: (Subtotal + postage revenue) excluding GST and converting to AUD
        tests:
          - not_null
      - name: sales_ex_gst_in_different_currency
        description: (Subtotal + postage revenue) excluding GST before converting to AUD
        tests:
          - not_null
      - name: profit
        description: Sales_ex_gst - Commission - COGS - Postage cost
        tests:
          - not_null
      - name: remoteness
        description: Flag to identify Remoteness, source-> dim.na_zone_settings
        tests:
          - not_null
      - name: is_split_order
        description: Split Order Status From OMS Order Header, When split order is not 0 then the original order splits into two or more orders
        tests:
          - not_null
      - name: customer_order_id
        description: customer_order_id                
  - name: channel_sku_revenue_90days
    description: This is the fact table of OMS, showing listing sku level, only contain 90 days data
    columns:
      - name: id
        description: source-> order_detial.id (OMS ID)
        tests:
          - not_null
          - unique
      - name: tid
        description: ID from each Channel
        tests:
          - not_null:
              config:
                severity: error
                error_if: ">100"
                warn_if: ">1"
      - name: currency
        description: |
          When store start with NZ the currency = NZ except NZDSOL
        tests:
          - not_null
      - name: sku
        tests:
          - not_null
      - name: order_no
        test:
          - not_null
      - name: subtotal
        description: SUM os sku sales price at that point of time including GST
        tests:
          - not_null
      - name: sales_ex_gst
        description: (Subtotal + postage revenue) excluding GST and converting to AUD
        tests:
          - not_null
      - name: sales_ex_gst_in_different_currency
        description: (Subtotal + postage revenue) excluding GST before converting to AUD
        tests:
          - not_null
      - name: profit
        description: Sales_ex_gst - Commission - COGS - Postage cost
        tests:
          - not_null
      - name: remoteness
        description: Flag to identify Remoteness, source-> dim.na_zone_settings
        tests:
          - not_null
      - name: is_split_order
        description: Split Order Status From OMS Order Header, When split order is not 0 then the original order splits into two or more orders
        tests:
          - not_null
      - name: customer_order_id
        description: customer_order_id                
  - name: order_flag_unioned
    description: Order Flag 
    columns:
      - name: order_no
        description: |
          OMS Order Number
        tests:
          - not_null
          - unique
      - name: modified
        description: System last modified time
        tests:
          - not_null
      - name: flag
        description:  information about why order are cancelled
      - name: modified_year
        description: Partition Column
        tests:
          - not_null
  - name: base_sku_replace
    description: A Historical Data For replacemnet SKU 
    columns:
      - name: sku_from
        description: |
          Original SKU before being replaced
        tests:
          - not_null
          - unique
      - name: sku_to
        description: replacement_sku
        tests:
          - not_null
  - name: base_sku_full
    description: A Historical Data For OMS "base_sku" table
    columns:
      - name: brand_id
        description: brand code
        tests:
          - not_null
      - name: category_id
        description: category code
        tests:
          - not_null
      - name: sku
        description: base sku
        tests:
          - not_null
      - name: created
        description: sku created time
        tests:
          - not_null
      - name: modified
        description: sku modified time
        tests:
          - not_null
      - name: cost_price
        description: average calculation result of this sku's cost
        tests:
          - not_null
      - name: length
        description: product length
        tests:
          - not_null
      - name: width
        description: product width
        tests:
          - not_null
      - name: height
        description: product height
        tests:
          - not_null
      - name: ingestion_time_utc
        description: snapshot time
        tests:
          - not_null
  - name: order_express_full
    description: A table that contains historical data about express level detail of oms orders. 
    columns:
      - name: id
        tests:
          - not_null
          - unique