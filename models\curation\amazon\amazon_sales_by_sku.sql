{{ config(
    materialized='incremental',
    unique_key="id",
    incremental_strategy="merge", 
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH unic AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_timestamp DESC) AS row_no
    FROM
        {{ get_source("amazon_api_lake", "amazon_sales_by_sku") }}
    {% if is_incremental() %}
        WHERE updated_date >= (SELECT DATE_SUB(MAX(updated_date), 7) FROM {{ this }})
    {% endif %}
)


SELECT
    r_id,
    id,
    report_id,
    report_date,
    parent_asin,
    child_asin,
    sku,
    sessions_total,
    session_percentage_total,
    sessions_browser,
    session_percentage_browser,
    sessions_mobile,
    session_percentage_mobile,
    page_views_total,
    page_views_percentage_total,
    page_views_browser,
    page_views_percentage_browser,
    page_views_mobile,
    page_views_percentage_mobile,
    featured_offer_buy_box_percentage,
    units_ordered,
    unit_session_percentage,
    ordered_product_sales,
    total_order_items,
    created_at,
    updated_at,
    updated_date
FROM unic
WHERE row_no = 1
