{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

---Below is the details for InboundShipment of NS
WITH
inboundshipment_pivot AS (
    SELECT
        shipmentnumber,
        shipmentstatus,
        actualshippingdate,
        actualdeliverydate,
        expecteddeliverydate,
        expectedshippingdate,
        INLINE(
            FROM_JSON(
                --noqa: disable=PRS
                itemslist :inboundshipmentitems,
                'array<struct<id:int,purchaseOrder:string,shipmentItem:string'
                ',shipmentItemDescription:string,poVendor:string,receivingLocation:string,quantityReceived:double,quantityExpected:double'
                ',quantityRemaining:double,unit:string,poRate:double,expectedRate:double,shipmentItemExchangeRate:double,shipmentItemEffectiveDate:date'
                ',unitLandedCost:double,totalUnitCost:double,shipmentItemAmount:double,poCurrency:string,incoterm:string>>'
            )
        ) AS (
            id, purchaseorder, shipmentitem, shipmentitemdescription, povendor, receivinglocation, quantityreceived, quantityexpected,
            quantityremaining, unit, porate, expectedrate, shipmentitemexchangerate, shipmentitemeffectivedate, unitlandedcost,
            totalunitcost, shipmentitemamount, pocurrency, incoterm
        ),
        --, shipmentmemo
        vesselnumber
    FROM {{ ref("netsuite_inbound_shipment") }}
)

SELECT
    shipmentnumber,
    shipmentstatus,
    actualshippingdate,
    actualdeliverydate,
    expecteddeliverydate,
    expectedshippingdate,
    --, externaldocumentnumber
    id,
    SUBSTRING(purchaseorder :name, 4) AS order_id, --cannot use right() as PO number has 10 or 11 letters e.g.PO00016736
    --noqa: disable=PRS
    shipmentitem :name AS sku,
    shipmentitemdescription,
    povendor,
    receivinglocation,
    quantityreceived,
    quantityexpected,
    quantityremaining,
    unit,
    porate,
    expectedrate,
    shipmentitemexchangerate,
    shipmentitemeffectivedate,
    unitlandedcost,
    totalunitcost,
    shipmentitemamount,
    pocurrency :name AS currenc,
    --noqa: enable=all
    incoterm,
    --, shipmentmemo
    vesselnumber
FROM
    inboundshipment_pivot
