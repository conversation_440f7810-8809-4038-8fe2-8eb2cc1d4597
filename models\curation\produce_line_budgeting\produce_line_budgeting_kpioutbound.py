from pyspark.sql.types import *
from pyspark.sql.functions import *
from pyspark.sql import Window
from pyspark.sql import DataFrame
from pyspark import pandas as ps
from functools import reduce
from datetime import datetime, timedelta

def model(dbt, session):
    dbt.config(
        materialized="incremental",
        incremental_strategy="merge",
        unique_key = "primary_key",
        tags=["prod", "databricks", "gcp" ],
    )
    edate = datetime.today()

    if dbt.is_incremental:
        date_ls = [str(edate - timedelta(days=x))[:10] for x in range(3)]
        data = get_data(date_ls, session, dbt)
        ##Deduplication Process
        window_spec = Window.partitionBy(['Subcate_id', 'Vendor_id', 'YearMonth']).orderBy(desc("ingestion_date"))
        data = data.withColumn('row_id',row_number().over(window_spec))
        data = data.where(col('row_id')==1)

        ## Assign Primary Key and data transformation
        data = data.select(
            col('Subcate_id').alias('subcate_id'),
            col('Vendor_id').alias('vendor_id'),
            col('YearMonth').alias('yearmonth'),
            col('ingestion_date').cast('date'),
            col('kpioutbound')
        ).withColumn(
            'primary_key',
            concat_ws(
                '-',
                'subcate_id',
                'vendor_id',
                'yearmonth',
                'ingestion_date'
            )
        )

        ##Extract latest record from current table
        old_data = session.sql(f'SELECt * FROM {dbt.this}')
        latest_ingestion = old_data.groupBy('subcate_id', 'vendor_id', 'yearmonth').agg(max('ingestion_date')).withColumnRenamed('max(ingestion_date)','ingestion_date')
        old_data = old_data.join(
            latest_ingestion,
            on = ['subcate_id', 'vendor_id', 'yearmonth', 'ingestion_date'],
            how = 'leftsemi'
        ).select('subcate_id', 'vendor_id', 'yearmonth', 'ingestion_date', 'kpioutbound', 'primary_key')

        unioned_table = old_data.union(data)        
        result=unioned_table.withColumn(
            "previous_kpioutbound",
            lead("kpioutbound").over(Window.partitionBy('subcate_id', 'vendor_id', "yearmonth").orderBy(desc("ingestion_date")))
        ).withColumn(
            "new_kpioutbound",
            lag("kpioutbound").over(Window.partitionBy('subcate_id', 'vendor_id', "yearmonth").orderBy(desc("ingestion_date")))
        ).where(
            ## Only extract records with differenct Forecast Qty##
            (col("kpioutbound") != col('previous_kpioutbound'))
            | col('previous_kpioutbound').isNull()
        ).select(
            'subcate_id',
            'vendor_id',
            'kpioutbound',
            "yearmonth",
            "ingestion_date",
            "primary_key"
        )

    else:
        sdate = datetime.strptime("2024-09-02", "%Y-%m-%d")
        date_ls = [str(sdate + timedelta(days=x))[:10] for x in range((edate - sdate).days)]
        data = get_data(date_ls, session, dbt)
        result = logic_handle(data)

    return result.pandas_api()

def _zero_fill(x):
    return "{0}-{1:0>2}".format(*x.split("-"))


def union_all(*dfs):
    return reduce(DataFrame.unionAll, dfs)


def get_data(date_ls, session, dbt):
    if dbt.config.get("is_gcp") == 'false':
        base_path = "abfss://<EMAIL>/produce_line_budgeting/kpi_outbound"
    else:
        base_path = "gs://lake-prod-mr7r/rawzone/produce_line_budgeting/kpi_outbound"
    path_ls = []
    for date in date_ls:
        year, month, day = date.split("-")
        path_ls.append(
            f"{base_path}/ingestion_date={year}-{month}-{day}/"
        )
    df_ls = []
    for path in path_ls:
        try:
            df = session.read.parquet(path)
            df.cache()
        except:
            continue
        df = df.withColumn("ingestion_date", to_date(from_unixtime(df['ingestion_timestamp'])))
        df = df.drop('ingestion_timestamp', 'Subcate', 'Vendor', 'record_date').pandas_api()
        data = ps.melt(df, id_vars=['Subcate_id', 'Vendor_id', 'ingestion_date'], var_name='YearMonth', value_name='kpioutbound').to_spark()
        zero_fill_udf = udf(_zero_fill, StringType())
        data = data.withColumn("YearMonth", zero_fill_udf(data['YearMonth']))
        df_ls.append(data)
    res_df = reduce(DataFrame.unionAll, df_ls)
    res_df = res_df.select(sorted(res_df.columns))
    return res_df

def logic_handle(data):
    w = Window.partitionBy('Subcate_id', 'Vendor_id', "yearmonth").orderBy("ingestion_date", "kpioutbound")

    # Define the window function to determine which rows have a changed kpioutbound value
    lag_kpioutbound = lag("kpioutbound", default=-1).over(w)
    has_changed_value = when(col("kpioutbound") != lag_kpioutbound, 1).otherwise(0)

    # Filter the DataFrame to keep only the first row for each changed value
    result_df = data.withColumn("has_changed_value", has_changed_value)\
                .filter(col("has_changed_value") == 1)\
                .select('Subcate_id', 'Vendor_id', "yearmonth", "kpioutbound", "ingestion_date")\
                .orderBy('Subcate_id', 'Vendor_id', "yearmonth", "ingestion_date")\
                .drop("has_changed_value")\
                .withColumn('primary_key', concat_ws('-', 'subcate_id', 'vendor_id', 'yearmonth', 'ingestion_date'))
    return result_df
