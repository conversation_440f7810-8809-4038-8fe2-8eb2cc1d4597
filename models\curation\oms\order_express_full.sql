{{ config(
    materialized='incremental',
    unique_key = 'id',
    incremental_strategy='merge',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('order_express') }}
{% if is_incremental() %}
    SELECT
        id,
        order_no,
        sku,
        num,
        express_code,
        express_no,
        modified,
        TO_DATE(FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) AS ingestion_date
    FROM {{ ref("order_express") }}
{% else %}
WITH unic AS(
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_date DESC) AS row_no
    FROM {{ get_source("lake", "order_express_full") }}
)
SELECT
    id,
    order_no,
    sku,
    num,
    express_code,
    express_no,
    modified,
    ingestion_date
FROM unic
WHERE row_no = 1
{% endif %}
