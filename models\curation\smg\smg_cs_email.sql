{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        a.id,
        a.f_user_id AS user_id,
        a.f_enterprise_id AS enterprise_id,
        CONCAT(a.f_sender_address, '-', a.f_subject) AS reference,
        LOWER(TRIM(a.f_email_account_emailname)) AS email_account,  ---Channel--
        IF(c.f_email_id IS NOT NULL, TRUE, FALSE) AS is_ignore,
        GREATEST(
            a.f_receive_date,
            a.f_ignore_date,
            b.f_sent_date
        ) AS watermark,  --unix
        FROM_UTC_TIMESTAMP(FROM_UNIXTIME(a.f_receive_date), 'Australia/Melbourne') AS receive_aest,
        FROM_UTC_TIMESTAMP(FROM_UNIXTIME(COALESCE(a.f_ignore_date, b.f_sent_date)), 'Australia/Melbourne') AS response_aest,
        ROW_NUMBER() OVER (
            PARTITION BY a.id ORDER BY GREATEST(
                a.f_receive_date,
                a.f_ignore_date,
                b.f_sent_date
            ) DESC
        ) AS row_id
    FROM
        {{ ref('dl_cs_email') }} AS a
        LEFT JOIN ---SP WAS INNER JOIN , LEFT JOIN in here to get ALL Receive EMAIL
            {{ ref('smg_cs_email_outbox') }} AS b
            ON
                a.f_email_id = b.f_ref_id
                AND
                a.f_recipient_address = b.f_sender
        LEFT JOIN
            {{ ref('dl_cs_email_operation') }} AS c
            ON
                c.f_email_id = a.id
    WHERE
        a.f_foldername NOT IN ('[Gmail]/Sent Mail', '[Gmail]/Spam')
)


SELECT *
FROM
    uniq
WHERE
    row_id = 1
