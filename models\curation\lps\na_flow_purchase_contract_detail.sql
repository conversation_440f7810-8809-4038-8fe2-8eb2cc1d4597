{{ config(
    materialized='table',
    indexes=[{'columns': ['business_id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    id,
    business_id,
    product_id,
    CASE
        WHEN currency = 1 THEN price_aud
        WHEN currency = 2 THEN price_rmb
        ELSE price_usd
    END AS purchase_contract_detail_price,
    currency,
    order_qty,
    CASE
        WHEN currency = 1 THEN order_value_aud
        WHEN currency = 2 THEN order_value_rmb
        ELSE order_value_usd
    END AS order_value,
    cartons,
    purchase_plan_detail_id,
    purchase_plan_id,
    purchase_plan_business_id,
    sku,
    flag_first_order
FROM
    {{
        get_source(
            "lake",
            "na_flow_purchase_contract_detail"
        )
    }}
