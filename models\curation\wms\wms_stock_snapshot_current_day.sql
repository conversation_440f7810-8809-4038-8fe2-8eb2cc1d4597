{{ config(
    materialized = 'view',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('inv_lot_loc_id') }}
-- depends_on: {{ ref('view_multiwarehouse') }}
SELECT
    --------Some SKU Dose not have warehouseid--------
    COALESCE(w.warehouseid, 'WH9999') AS warehouseid,
    UPPER(TRIM(i.sku)) AS sku,
    CAST(SUM(COALESCE(i.qty, 0)) AS INT) AS qtytotal,
    CAST(SUM(COALESCE(i.qtyallocated, 0)) AS INT) AS qtyallocated,
    CAST(SUM(COALESCE(i.qtyonhold, 0)) AS INT) AS qtyonhold,
    CAST(SUM(COALESCE(i.qty, 0) - COALESCE(i.qtyallocated, 0) - COALESCE(i.qtyonhold, 0)) AS INT) AS qtyavail,
    DATE_ADD(FROM_UTC_TIMESTAMP(CURRENT_TIMESTAMP(), 'Australia/Melbourne'), -1) AS snapshot_date,
    CONCAT(
        COALESCE(w.warehouseid, 'WH9999'),
        '-',
        UPPER(TRIM(i.sku)),
        '-',
        DATE_ADD(FROM_UTC_TIMESTAMP(CURRENT_TIMESTAMP(), 'Australia/Melbourne'), -1)
    ) AS primary_key,
    YEAR(DATE_ADD(FROM_UTC_TIMESTAMP(CURRENT_TIMESTAMP(), 'Australia/Melbourne'), -1)) AS snapshot_year
FROM
    {{ ref("inv_lot_loc_id") }} AS i
    LEFT JOIN
        {{ ref("view_multiwarehouse") }} AS w
        ON i.locationid = w.locationid
WHERE
    i.locationid != 'LOST'
    AND i.locationid IS NOT NULL
GROUP BY
    UPPER(TRIM(i.sku)),
    w.warehouseid,
    CONCAT(
        COALESCE(w.warehouseid, 'WH9999'),
        '-',
        UPPER(TRIM(i.sku)),
        '-',
        DATE_ADD(FROM_UTC_TIMESTAMP(CURRENT_TIMESTAMP(), 'Australia/Melbourne'), -1)
    ),
    YEAR(DATE_ADD(FROM_UTC_TIMESTAMP(CURRENT_TIMESTAMP(), 'Australia/Melbourne'), -1))
