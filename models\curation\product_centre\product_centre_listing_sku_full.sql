{{ config(
    materialized = 'incremental',
    unique_key = 'unique_key',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('product_centre_listing_sku') }}
SELECT
    overall_packagecbm,
    overall_package_height,
    overall_package_length,
    overall_package_weight,
    overall_package_width,
    assembly_required,
    available_quantity,
    backend_keywords,
    brand,
    bundle,
    button_battery,
    buyer,
    buying_team,
    category,
    category_team,
    colour,
    compliance_standard,
    cost_price,
    create_at,
    create_by,
    description,
    ean,
    ebay_title,
    extended_listing_sku,
    feature,
    individualcbm,
    individual_height,
    individual_length,
    individual_weight,
    individual_width,
    is_parent,
    lps_created_at,
    lps_product_id,
    lps_updated_at,
    marketing_specialist,
    materials,
    new_aim_title,
    overall_assembledcbm,
    overall_assembled_height,
    overall_assembled_length,
    overall_assembled_weight,
    overall_assembled_width,
    package_content,
    postage_avg_cost,
    replacement_sku,
    rrp,
    save_date,
    seasonality,
    shipping_class,
    sku,
    sku_status,
    specification,
    status,
    sub_category,
    sync_date,
    taxonomy,
    update_at,
    update_by,
    valid,
    warehouse,
    warranty_period,
    category_attributes,
    google_sync_status,
    oms_sync_status,
    lps_sync_status,
    delivery_date,
    file_drive_id,
    product_manual,
    specific_changes,
    stock_control_buyer,
    stock_control_team,
    ebay_video_link,
    enable_date,
    listing_status,
    oms_sync_date,
    original_replacement_sku,
    youtube_link,
    eta_delivery_date,
    l1category,
    l1id,
    l2id,
    l2sub_category,
    l3id,
    l3sub_category,
{% if is_incremental() %}
    CURRENT_DATE() AS ingestion_date,
    CONCAT(CURRENT_DATE(), '-', sku) AS unique_key
FROM {{ ref("product_centre_listing_sku") }}
{% else %}
    to_date({{ input_file_name_substring(97, 10) }}) as ingestion_date,
    concat({{ input_file_name_substring(97, 10) }}, '-', sku) AS unique_key
from {{ get_source("pc_lake", "product_centre_listing_sku_full") }}
WHERE
    is_parent = 'No'
    AND sku IS NOT NULL
    --- Status Filter---
    AND status IN (5, 6)
{% endif %}
