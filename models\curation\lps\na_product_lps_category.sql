{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

-- depends_on: {{ ref('na_product_lps_category_raw') }}
WITH uniqe AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(updated_at, created_at) DESC) AS row_id
    FROM
        {{ ref('na_product_lps_category_raw') }}
)

SELECT * EXCEPT (row_id)
FROM
    uniqe
WHERE
    row_id = 1
