{{ config(
    materialized = 'incremental',
    unique_key = 'id',
    incremental_strategy = 'merge',
) }}

/*
Purpose:
This SQL script is designed to track the Service Level Agreement (SLA) compliance
of SKU tracking crawlers. It processes raw data from the `channel_health_check` table,
calculates the success and failure rates of requests, and materializes the results
incrementally.

High-Level Design:
1. Configuration: The script is configured to run incrementally, merging new data based on the unique key `id`.
2. Raw Data Extraction: Extracts raw data from the `channel_health_check` table, generating a
unique `id` for each record and filtering for new data if running incrementally.
3. Data Processing: Filters the raw data to retain only the latest record for each combination
of `channel`, `pid`, and `ingestion_date`.
4. SLA Calculation: Aggregates the processed data to calculate total requests, total failed requests,
total successful requests, and their respective percentages.
5. Final Output: Selects and outputs the calculated SLA metrics.
6. Remove catch channel as the catch closed.
*/

WITH sla_raw AS (
    SELECT
        ingestion_date,
        channel,
        COUNT(*) AS total_requests,
        SUM(CASE
            WHEN response_status IN (999, 403) THEN 1
            ELSE 0
        END) AS total_failed,
        SUM(CASE
            WHEN response_status NOT IN (999, 403) THEN 1
            ELSE 0
        END) AS total_success,
        ROUND(
            SUM(CASE WHEN response_status IN (999, 403) THEN 1 ELSE 0 END) * 100.0 / COUNT(*),
            2
        ) AS failed_percentage,
        ROUND(
            SUM(CASE WHEN response_status NOT IN (999, 403) THEN 1 ELSE 0 END) * 100.0 / COUNT(*),
            2
        ) AS success_percentage,
        CONCAT(channel, "-", ingestion_date) AS id
    FROM
        {{ ref("channel_health_check") }}
    WHERE channel != "catch"
    GROUP BY
        channel, ingestion_date
)

SELECT * FROM sla_raw;
