{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        ticket_id,
        user_id,
        message_id,
        IF(from_name = 'OneShop', 'admin', 'customer') AS role_,
        FROM_UTC_TIMESTAMP(message_created_date, 'Australia/Melbourne') AS created_aest,
        FROM_UTC_TIMESTAMP(replied_date, 'Australia/Melbourne') AS replied_time_aest,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(replied_date, message_created_date) DESC) AS row_id,
        COALESCE(replied_date, message_created_date) AS watermark
    FROM
        {{ get_source('lake','dl_kogan_message') }}
)

SELECT *
FROM
    uniq
WHERE
    row_id = 1
