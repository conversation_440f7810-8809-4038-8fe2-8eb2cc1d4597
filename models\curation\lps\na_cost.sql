{{ config(
    materialized='table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        order_shipping_plan_id,
        currency,
        rate_aud_to_rmb,
        rate_aud_to_usd,
        custom_processing_fee_aud,
        custom_processing_fee_rmb,
        custom_processing_fee_usd,
        status,
        creator_id,
        creator_cn_name,
        creator_en_name,
        created_at,
        updated_at,
        department_id,
        department_cn_name,
        department_en_name,
        sailing_days,
        retd,
        reta,
        order_shipping_plan_business_id,
        total_shipping_cbm,
        payment_sum_aud,
        payment_sum_usd,
        payment_sum_rmb,
        freight_sum_aud,
        freight_sum_usd,
        freight_sum_rmb,
        freight_container_qty,
        charge_sum_aud,
        charge_sum_usd,
        charge_sum_rmb,
        tariff_sum_rmb,
        tariff_sum_usd,
        tariff_sum_aud,
        value_sum_aud,
        value_sum_rmb,
        value_sum_usd,
        other_sum_aud,
        other_sum_usd,
        other_sum_rmb,
        write_off_sum_aud,
        write_off_sum_rmb,
        write_off_sum_usd,
        value_gst_aud,
        value_gst_rmb,
        value_gst_usd,
        local_gst_aud,
        local_gst_rmb,
        local_gst_usd,
        order_numbers,
        purchase_type,
        flag_approve,
        domestic_freight_inv,
        sea_freight_inv,
        duty_invoice,
        disbursement_inv,
        original_port_cost_aud,
        original_port_cost_rmb,
        original_port_cost_usd,
        shipping_fee_type,
        shipping_method,
        ROW_NUMBER() OVER (PARTITION BY order_numbers ORDER BY updated_at DESC) AS row_id
    FROM
        {{
            get_source(
                "lake",
                "na_cost"
            )
        }}
    WHERE
        order_numbers IS NOT NULL
)


SELECT
    id,
    order_shipping_plan_id,
    currency,
    rate_aud_to_rmb,
    rate_aud_to_usd,
    custom_processing_fee_aud,
    custom_processing_fee_rmb,
    custom_processing_fee_usd,
    status,
    creator_id,
    creator_cn_name,
    creator_en_name,
    created_at,
    updated_at,
    department_id,
    department_cn_name,
    department_en_name,
    sailing_days,
    retd,
    reta,
    order_shipping_plan_business_id,
    total_shipping_cbm,
    payment_sum_aud,
    payment_sum_usd,
    payment_sum_rmb,
    freight_sum_aud,
    freight_sum_usd,
    freight_sum_rmb,
    freight_container_qty,
    charge_sum_aud,
    charge_sum_usd,
    charge_sum_rmb,
    tariff_sum_rmb,
    tariff_sum_usd,
    tariff_sum_aud,
    value_sum_aud,
    value_sum_rmb,
    value_sum_usd,
    other_sum_aud,
    other_sum_usd,
    other_sum_rmb,
    write_off_sum_aud,
    write_off_sum_rmb,
    write_off_sum_usd,
    value_gst_aud,
    value_gst_rmb,
    value_gst_usd,
    local_gst_aud,
    local_gst_rmb,
    local_gst_usd,
    order_numbers,
    purchase_type,
    flag_approve,
    domestic_freight_inv,
    sea_freight_inv,
    duty_invoice,
    disbursement_inv,
    original_port_cost_aud,
    original_port_cost_rmb,
    original_port_cost_usd,
    shipping_fee_type,
    shipping_method
FROM
    uniq
WHERE
    row_id = 1
