{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

--V 7 days receive brand
SELECT
    a.id,
    b.channel,
    'Email' AS type_
FROM
    {{ ref('smg_cs_email') }} AS a
    LEFT JOIN
        {{ ref('smg_email_account') }} AS b
        ON
            a.email_account = LOWER(TRIM(b.email_account))
WHERE
    ---- Channels
    b.email_account IN (
        '<EMAIL>', --Cefito
        '<EMAIL>', --Everfit
        '<EMAIL>', --iPet
        '<EMAIL>', --Artiss
        '<EMAIL>', --Devanti
        '<EMAIL>', --Giselle
        '<EMAIL>', --RigoKids
        '<EMAIL>',  --Keezi
        '<EMAIL>',  --Weisshorn
        '<EMAIL>', --Gardeon
        '<EMAIL>', --Comfee
        '<EMAIL>', --Artissin
        '<EMAIL>', --Cosyclub
        '<EMAIL>' --eBay
    )

---------------------------------------------------------
--eBay Message
UNION

SELECT
    id,
    'eBay' AS channel,
    'Message' AS type_
FROM
    {{ ref('smg_ebay_message') }}
WHERE
    sender != 'ozplaza.living'

UNION

--eBay Feedback
SELECT
    id,
    'eBay' AS channel,
    'Feedback-NEG' AS type_
FROM
    {{ ref('smg_cs_feedback') }}
WHERE
    is_ignore IS FALSE
    AND
    comment_type = 1

UNION

--eBay Case
SELECT
    id,
    'eBay' AS channel,
    'Case' AS type_
FROM
    {{ ref('smg_cs_case') }}
WHERE
    is_ignore IS FALSE

UNION
--eBay Return
SELECT
    id,
    'eBay' AS channel,
    'Return' AS type_
FROM
    {{ ref('smg_cs_case_return') }}
WHERE
    is_ignore IS FALSE
