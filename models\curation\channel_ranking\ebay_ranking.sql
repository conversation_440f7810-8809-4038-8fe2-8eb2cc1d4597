{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT DISTINCT
    rank,
    COALESCE(title, 'UNKNOWN') AS title,
    item_url,
    item_id,
    price,
    sponsored,
    postage,
    page,
    keyword,
    capture_at,
    seller_name,
    brand_name,
    specifics_item,
    details_item,
    dimension,
    features,
    description_link,
    sold_price,
    sold_price_max,
    variation_sku,
    TO_DATE(FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) AS ingestion_date
FROM {{ get_source("lake", "ebay_ranking") }}
WHERE capture_at IS NOT NULL
