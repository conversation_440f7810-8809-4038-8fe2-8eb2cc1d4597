{{ config(
    materialized = 'incremental',
    unique_key = 'internalid',
    parition_by = 'ingestion_year',
    file_format = 'delta',
    incremental_strategy='merge',
    on_schema_change='append_new_columns',
    partition_by = 'ingestion_year',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('netsuite_inbound_container_items_raw') }}

--custrecord_acs_inbound_shipment_line_id are all NULL values and cannot find business values

WITH raw_ AS (
    SELECT
        internalid,
        INLINE(
            FROM_JSON(
                --noqa: disable=PRS
                customfieldlist :customfield[*],
                'array<struct<value:string,scriptId:string>>'
            --noqa: enable=all
            )
        ) AS (value_, scriptid),
        ingestion_date,
        ingestion_year
    FROM
        {{ ref ('netsuite_inbound_container_items_raw') }}
    WHERE
        internalid NOT IN (1, 2, 3, 4)  --Test Records
        {% if is_incremental() %}
            AND
            ingestion_year >= YEAR(CURRENT_DATE()) - 1
            AND
            ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }} WHERE ingestion_year >= YEAR(CURRENT_DATE()) - 1)
        {% endif %}
)

SELECT
    internalid,
    --noqa: disable=PRS
    custrecord152 :name AS seal_number,
    REPLACE(custrecord_acs_child_po :name, 'Purchase Order #', '') AS po,
    custrecord_acs_container_sku: name AS sku,
    --noqa: enable=all
    INT(COALESCE(custrecord_acs_item_qty_cont_remaining, 0)) AS qty_remaining,
    INT(custrecord_acs_item_qty_container) AS qty,
    --noqa: disable=PRS
    custrecord_acs_parent_inbound_ship: name AS inbound_shipment,
    custrecord_acs_vend_container :name AS vendor_container,
    --noqa: enable=all
    custrecord_na_freeze AS freeze,
    ingestion_date,
    ingestion_year
FROM
    raw_ PIVOT (
            MAX(value_) FOR scriptid IN (
                'custrecord152',
                'custrecord_acs_child_po',
                'custrecord_acs_container_sku',
                'custrecord_acs_item_qty_cont_remaining',
                'custrecord_acs_item_qty_container',
                'custrecord_acs_parent_inbound_ship',
                'custrecord_acs_vend_container',
                'custrecord_na_freeze'
            )
    )
