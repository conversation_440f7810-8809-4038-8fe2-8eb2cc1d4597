version: 2

models:
  - name: smg_bunnings_message
    description: Message From SMG for bunnings
    columns:
      - name: id
        description: |
          Primary Key
        tests:
          - not_null
          - unique
      - name: role_
        description: From Where
        tests:
          - not_null
      - name: created_aest
        description: |
          The time when a record created from the system
        tests:
          - not_null
  - name: smg_catch_message
    description: Message From SMG for Catch
    columns:
      - name: id
        description: |
          Primary Key
        tests:
          - not_null
          - unique
      - name: role_
        description: From Where
        tests:
          - not_null
      - name: created_aest
        description: |
          The time when a record created from the system
        tests:
          - not_null
  - name: smg_dsz_message
    description: Message From SMG for Dropshipzone
    columns:
      - name: id
        description: |
          Primary Key
        tests:
          - not_null
          - unique
      - name: role_
        description: From Where
        tests:
          - not_null
      - name: created_aest
        description: |
          The time when a record created from the system
        tests:
          - not_null
  - name: smg_ebay_message
    description: Message From SMG for eBay
    columns:
      - name: id
        description: |
          Primary Key
        tests:
          - not_null
          - unique
      - name: watermark
        description: |
          Water Mark Column
        tests:
          - not_null
      - name: reference_id
        description: |
          Ticket ID used by CS Team As a reference
        tests:
          - not_null
  - name: smg_kogan_message
    description: Message From SMG for Kogan
    columns:
      - name: id
        description: |
          Primary Key
        tests:
          - not_null
          - unique
      - name: role_
        description: From Where
        tests:
          - not_null
      - name: created_aest
        description: |
          The time when a record created from the system
        tests:
          - not_null
  - name: smg_mydeal_message
    description: Message From SMG for Mydeal
    columns:
      - name: id
        description: |
          Primary Key
        tests:
          - not_null
          - unique
      - name: role_
        description: From Where
        tests:
          - not_null
      - name: created_aest
        description: |
          The time when a record created from the system
        tests:
          - not_null
  - name: smg_mysale_message
    description: Message From SMG for Mysale
    columns:
      - name: id
        description: |
          Primary Key
        tests:
          - not_null
          - unique
      - name: role_
        description: From Where
        tests:
          - not_null
      - name: created_aest
        description: |
          The time when a record created from the system
        tests:
          - not_null
  - name: smg_all_replied
    description: SMG Union Table for Response Time
    columns:
      - name: reference
        description: |
          Ticket ID for each Channel
        tests:
          - not_null
      - name: role_
        description: From Where
        tests:
          - not_null
      - name: created_aest
        description: |
          The time when a record created from the system
        tests:
          - not_null
      - name: channel
        description: |
          Channel Name
        tests:
          - not_null
  - name: smg_cs_email
    description: SMG email derived from dl_cs_email, dl_cs_email_outbox, dl_cs_email_operation
    columns:
      - name: id
        description: |
          ID from dl_cs_email
        tests:
          - not_null
          - unique
      - name: reference
        description: |
          Ticket ID for each Channel, A combination: sender_address + subject
        tests:
          - not_null
      - name: watermark
        description: Watermark column for incrmental
        tests:
          - not_null

  - name: faulty_raw
    description: |
      the fundamental table for faulty fact
    columns:
      - name: uniq_key
        description: |
          dl_ticket_sku uniq_keu. combination f_sku + f_ticket_id + f_problem_reason
        tests:
          - not_null
          - unique
      - name: order_no
        description: |
          OMS order No
        tests:
          - not_null
      - name: handle_status
        description: |
          the status of faulty ticket
        tests:
          - not_null
      - name: created_at
        description: |
          the time when a ticket is created
        tests:
          - not_null
      - name: ticket_no
        tests:
          - not_null
      - name: sku
        description: |
          Base SKU
        tests:
          - not_null
      - name: order_status
        descriptin: Order Status
        tests:
          - not_null
      - name: modified
        description: last update time from dl_ticket
        tests:
          - not_null
      - name: modified_year
        description: parition column
        tests:
          - not_null
  - name: dl_ticket_sku_30days
    description: This is a 30 days dl_ticket_sku only
    columns:
      - name: uniq_key
        description: |
          Combination Key-> SKU + Ticket ID + Problem Reason, one ticket ID for ONE listing for One problme
        tests:
          - not_null
          - unique
      - name: f_ticketid
        description: |
          Foreign key for dl_ticket_sku
        tests:
          - not_null
      - name: f_sku
        description: Base SKU
        tests:
          - not_null
      - name: modified
        description: last update time from dl_ticket
        tests:
          - not_null
  - name: dl_ticket_sku
    description: Full table for dl_ticket_sku
    columns:
      - name: uniq_key
        description: |
          Combination Key SKU + Ticket No + Problem Reason
        tests:
          - not_null
          - unique
      - name: f_ticketid
        description: |
          Foreign key for dl_ticket_sku
        tests:
          - not_null
      - name: modified
        desciption: |
          Last update time from dl_ticket
        tests:
          - not_null
      - name: modified_year
        description: |
          Year(modified)
        tests:
          - not_null
  - name: dl_ticket
    description: Full table for dl_ticket
    columns:
      - name: id
        description: |
          System ID
        tests:
          - not_null
          - unique
      - name: modified
        desciption: |
          Last update time from dl_ticket
        tests:
          - not_null
      - name: modified_year
        description: |
          Year(modified)
        tests:
          - not_null
      - name: f_handle_status
        description: |
          Ticket Status, Closed or Open
      - name: ingestion_time_utc
        desciption: |
          data ingestion timestamp
        tests:
          - not_null