{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH uniq AS (
    SELECT
        id,
        order_id,
        user_id,
        dsz_id,
        IF(from_type = 'SHOP', 'admin', 'customer') AS role_,
        FROM_UTC_TIMESTAMP(date_created, 'Australia/Melbourne') AS created_aest, --sent_time
        FROM_UTC_TIMESTAMP(replied_date, 'Australia/Melbourne') AS replied_time_aest,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(replied_date, date_created) DESC) AS row_id,
        COALESCE(replied_date, date_created) AS watermark
    FROM
        {{ get_source('lake','dl_dsz_message') }}
)

SELECT *
FROM
    uniq
WHERE
    row_id = 1
