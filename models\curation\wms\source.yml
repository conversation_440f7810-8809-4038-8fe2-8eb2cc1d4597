version: 2

sources:
  - name: wms_lake
    schema: parquet
    tables:
      - name: tmp_so_eparcel_label
        identifier: abfss://<EMAIL>/Databricks/WMS/tmp_so_eparcel_label/latest.parquet
        quoting:
          identifier: true
      - name: tmp_so_eparcel_label_partition
        identifier: abfss://<EMAIL>/Databricks/WMS/tmp_so_eparcel_label/partition_folder/*/*.parquet
        quoting:
          identifier: true
      - name: tmp_so_eparcel_label_history
        identifier: abfss://<EMAIL>/Databricks/WMS/tmp_so_eparcel_label/historical_folder/*.parquet
        quoting:
          identifier: true
      - name: bas_package
        identifier: abfss://<EMAIL>/Databricks/WMS/bas_package/latest.parquet
        quoting:
          identifier: true
