{{ config(
    materialized='table',
    indexes=[{'columns': ['keywords']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH base AS (
    SELECT
        CASE
            WHEN INSTR(page_path, '.html') > 0 THEN 1
            ELSE 0
        END AS details,
        TRIM(REPLACE(PARSE_URL(pre_page_path, 'QUERY', 'q'), '+', ' ')) AS keywords,
        COALESCE(PARSE_URL(pre_page_path, 'QUERY', 'p'), 1) AS page,
        pre_page_path,
        date AS event_date,
        date_hour
    FROM
        {{ ref("dsz_ua_previous_page") }}
    WHERE
        hostname = 'www.dropshipzone.com.au'
        AND INSTR(pre_page_path, 'catalogsearch') > 0
)

SELECT
    SUM(details) AS deatil_views,
    page,
    keywords,
    event_date,
    date_hour
FROM
    base
WHERE NOT REGEXP_LIKE(keywords, '^[A-Z0-9-]+$')
GROUP BY keywords, page, event_date, date_hour
