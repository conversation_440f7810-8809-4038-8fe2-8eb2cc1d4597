{{ config(
        materialized = "incremental",
        partition_by = "ingestion_date",
        unique_key = "id",
        incremental_strategy = "merge",
        tags = [ "prod", "databricks", "gcp" ]
) }}

-- depends_on: {{ ref('dsz_product') }}

WITH raw AS (
    SELECT
        id,
        title,
        price,
        rating,
        rating_count,
        seller_name,
        brand_name,
        capture_at,
        image_url,
        additional_fields,
        live_on_site,
        sku,
        pid,
        url,
        channel,
        response_status,
        ingestion_date
    FROM {{ ref("channel_health_check_raw") }}
    {% if is_incremental() %}
        WHERE ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
    {% endif %}
)
,
{% if is_incremental() %}
    latest_dsz_sku_list AS (
        SELECT
            sku,
            url,
            ingestion_date
        FROM {{ get_source("crawler_lake", "na_dropshipzone_sku_list") }}
        WHERE ingestion_date = (SELECT MAX(ingestion_date) FROM {{ get_source("crawler_lake", "na_dropshipzone_sku_list") }})
    )
    ,
    dsz AS (
        SELECT
            CONCAT_WS("-", "dropshipzone", b.entity_id, a.ingestion_date) AS id,
            b.title,
            b.price,
            NULL AS rating,
            NULL AS rating_count,
            b.vendor_id AS seller_name,
            b.brand AS brand_name,
            b.capture_at,
            NULL AS image_url,
            NULL AS addtional_fields,
            IF(b.product_status = 1, "True", "False") AS live_on_site,
            a.sku,
            b.entity_id AS pid,
            a.url,
            "dropshipzone" AS channel,
            NULL AS response_status,
            a.ingestion_date
        FROM latest_dsz_sku_list AS a
            LEFT JOIN {{ ref("dsz_product") }} AS b ON a.sku = b.sku
    )

{% else %}

dsz AS (
    select CONCAT_WS("-", "dropshipzone", b.entity_id, a.ingestion_date) AS id, b.title, b.price, null as rating, null as rating_count,
    b.vendor_id as seller_name, b.brand as brand_name, b.capture_at, null as image_url, null as addtional_fields,
    if(b.product_status = 1, "True", "False") AS live_on_site, a.sku, b.entity_id as pid, a.url, "dropshipzone" AS channel, null as response_status, a.ingestion_date
from {{ ref("na_dropshipzone_sku_full") }} as a
left join {{ ref("dsz_product_history") }} as b on a.sku = b.sku and a.ingestion_date = b.ingestion_date
where b.sku is not null     -- Some SKUs from Googlesheet SKU list are earlier than DSZ product API ingestion
)
{% endif %}

SELECT *
FROM raw
UNION
SELECT *
FROM dsz
