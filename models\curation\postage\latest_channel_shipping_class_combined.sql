{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ],
    post_hook = "{{ post_hook_export_parquet(this) }}"
) }}

WITH latest_v1_table AS (
    SELECT *
    FROM
        {{ ref("ryan_all_channel_shipping_class") }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY sku, channel_type ORDER BY generate_date DESC) = 1
),

latest_v2_table AS (
    SELECT *
    FROM
        {{ ref("all_channel_shipping_class_unpvt_v2") }}
    QUALIFY ROW_NUMBER() OVER (PARTITION BY sku, channel_type ORDER BY generate_date DESC) = 1

),

union_tables AS (
    SELECT
        sku,
        channel_type,
        shipping_class,
        national_flat_rate_excl_gst,
        generate_date,
        'v2' AS source_table
    FROM latest_v2_table
    UNION ALL
    SELECT
        a.sku,
        a.channel_type,
        a.shipping_class,
        a.national_flat_rate_excl_gst,
        a.generate_date,
        'v1' AS source_table
    FROM latest_v1_table AS a
        LEFT JOIN
            latest_v2_table AS b ON a.sku = b.sku AND a.channel_type = b.channel_type
    WHERE b.sku IS NULL
),

ranked_data AS (
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY sku, channel_type ORDER BY
                CASE
                    WHEN source_table = 'v2' THEN 1 -- Prioritize v2 Table
                    ELSE 2
                END,
                generate_date DESC -- Then latest date in case of ties (though with our UNION ALL logic, this should mostly handle itself)
        ) AS rn
    FROM
        union_tables
),

national_flat_rate AS (
    SELECT DISTINCT
        sku,
        national_flat_rate_excl_gst
    FROM {{ ref("all_channel_shipping_class_unpvt_v2") }}
)

SELECT
    a.sku,
    a.channel_type,
    CASE
        WHEN a.shipping_class IS NULL THEN NULL
        WHEN TRY_CAST(a.shipping_class AS DOUBLE) IS NOT NULL
            THEN -- Check if it's a number (float or int)
                CASE
                    -- It's an integer
                    WHEN a.shipping_class = FLOOR(CAST(a.shipping_class AS DOUBLE)) THEN CAST(CAST(a.shipping_class AS INT) AS STRING)
                    ELSE CAST(a.shipping_class AS STRING) -- It's a float with decimal
                END
        ELSE CAST(a.shipping_class AS STRING) -- Already a string or unparseable
    END AS shipping_class,
    b.national_flat_rate_excl_gst,
    a.generate_date
FROM ranked_data AS a
    LEFT JOIN
        national_flat_rate AS b
        ON a.sku = b.sku
WHERE a.rn = 1
