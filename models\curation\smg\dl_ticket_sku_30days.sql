{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('dl_ticket_sku_raw') }}
-- depends_on: {{ ref('dl_ticket_raw') }}

/*
Unlike OMS, dl_ticket_sku does not update data in the original row if data is modified; instead,
 it creates a new row in which dl_ticket_sku is more like a log table, not a fact table.
*/

WITH
uniq AS (
    SELECT
        a.f_ticketid,
        a.f_sku,
        a.f_job_no,
        a.f_has_problem,
        a.f_big_level,
        a.f_second_level,
        a.f_problem_reason,
        a.f_ordered_qty,
        a.f_unit_cost,
        CAST(
            COALESCE(
                b.f_last_updatetime,
                b.f_add_time
            ) AS TIMESTAMP
        ) AS modified,
        ROW_NUMBER() OVER (PARTITION BY a.f_ticketid, a.f_sku ORDER BY a.id DESC) AS row_id
    FROM
    --dl_ticket is a header and dl_ticket is detail , always use inner join to filter out delete id from detail table
        {{ ref('dl_ticket_sku_raw') }} AS a,
        {{ ref('dl_ticket_raw') }} AS b
    WHERE
        a.f_ticketid = b.id
)

SELECT
    CONCAT_WS(
        '@',
        f_ticketid,
        f_sku
    ) AS uniq_key,
    f_ticketid,
    f_sku,
    f_job_no,
    f_has_problem,
    f_big_level,
    f_second_level,
    f_problem_reason,
    f_ordered_qty,
    f_unit_cost,
    modified
FROM
    uniq
WHERE
    row_id = 1 AND modified >= DATE_SUB(CURRENT_DATE(), 30)
