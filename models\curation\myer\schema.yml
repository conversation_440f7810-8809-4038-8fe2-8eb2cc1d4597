version: 2

models:

  - name: myer_sku_status
    description: This table is contains the myer sku listing status. Combined price column.
    columns:
    - name: id
      description: id
      tests: 
        - not_null
        - unique
    - name: new_aim_active_sku
      description: New Aim acitive sku at Myer.
      tests: 
        - not_null
        - relationships:
            to: ref('listing_sku_attributes')
            field: sku
            severity: warn
    - name: level_1_code
      description: Product code for each sku, can be used to do direct search on Myer website.
      tests: 
        - not_null
    - name: live_on_site
      description: Listing status of the sku.
      tests: 
        - not_null
    - name: price
      description: Current price of the listing.
    - name: ingestion_date
      description: Date of ingestion.
      tests: 
        - not_null 
    - name: capture_at
      description: Time when data is captured.