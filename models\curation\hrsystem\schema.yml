version: 2

models:
  - name: employmenthero_leaverequest
    description: |
      Employees' leave request from crawler
    columns:
      - name: id
        description: |
          Leave request uuid, supposed to be unique
        tests:
          - not_null
          - unique
      - name: totalhours
        description: |
          Leave hours amount
        tests:
          - not_null
      - name: employeeid
        description: |
          Employees' external id
      - name: leave_balance_amount
        description: |
          How many hours left for this leave_category and this employee
        tests:
          - not_null
      - name: leavecategory
        description: |
          Leave name
        tests:
          - not_null
      - name: fromdate
        description: |
          Leave start date
        tests:
          - not_null
      - name: todate
        description: |
          Leave end date
        tests:
          - not_null
      - name: status
        description: |
          Leave request result
        tests:
          - not_null
      - name: ingestion_time_utc
        description: |
          ingestion time utc
        tests:
          - not_null