version: 2

models:
  - name: dsz_sku_google_result_latest
    description: |
      Result from dsz sku google search result crawler for aiorxy in latest 90 days.
    columns:
      - name: id
        description: |
          Unique id of the row.
        tests:
          - not_null
          - unique
      - name: sku
        description: |
          The full SKU of the dsz product.
        tests:
          - not_null
      - name: title
        description: |
          The title of the dsz product.
      - name: link
        description: |
          The URL link of like-for-like product from Google search.
          - not_null
      - name: web_title
        description: |
          The title of that particular Google search result.
      - name: rank
        description: |
          The search ranking of that particular Google search result.
        tests:
          - not_null
      - name: image
        description: |
          The image url of the product.
      - name: capture_date
        description: |
          The date when this information is captured.
        tests:
          - not_null
      - name: search_by_sku
        description: |
          Indicates if it is a SKU must-included Google search result or just search result using product title.
        tests:
          - not_null
  - name: google_serp_crawler_sku_list
    description: |
      the list of sku need to be crawled by google serp crawler. (new dsz sku + old sku that need to update its google search result)
    columns:  
      - name: sku
        description: |
          The SKU of the product.
        tests:
          - not_null
          - unique
      - name: title
        description: |
          The title of the product.
          - not_null
      - name: update_date
        description: |
          The lastest date when its google search result got updated.
          - not_null
      - name: next_update_date
        description: |
          The next date when its google search result should get update.
          - not_null
  - name: user_submit_url_list
    description: |
      the list of url need to be crawled by crawler for airoxy.
    columns:  
      - name: url
        description: |
          The url of the products.
        tests:
          - not_null
          - unique
      - name: url_hash
        description: |
          The url hash of the products.
        tests:
          - not_null
          - unique
      - name: update_date
        description: |
          The date this list get update.
        tests:
          - not_null  
  - name: user_submit_url_result
    description: |
      the result from crawler for airoxy user submit url.
    columns:
      - name: id
        description: |
          product id
        tests:
          - not_null
          - unique
      - name: capture_date
        description: |
          date the data is collected
        tests:
          - not_null
      - name: Channel
        description: |
          the domain of url
      - name: url
        description: |
          url link of the product
        tests:
          - not_null
      - name: url_hash
        description: |
          url hash of link
        tests:
          - not_null
      - name: price
        description: |
          price of the product
        tests:
          - not_null
      - name: image
        description: |
          image link of the product
      - name: title
        description: |
          Title of the product.