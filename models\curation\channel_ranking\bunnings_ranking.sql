{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    raw_data,
    brandname,
    sellername,
    name,
    code,
    rating,
    ratingcount,
    price,
    productcount,
    title,
    product_id,
    ranking,
    CEIL(ranking / 36.0) AS page,
    keyword,
    url,
    capture_at,
    TO_DATE(FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) AS ingestion_date
FROM
    {{ get_source("lake", "bunnings_ranking") }}
WHERE capture_at IS NOT NULL AND raw_data IS NOT NULL
