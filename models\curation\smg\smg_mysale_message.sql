{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        ticket_id,
        user_id,
        message_id,
        message_created_date AS created_aest,
        IF(is_seller = 1, 'admin', 'customer') AS role_, --watermark
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY message_created_date DESC) AS row_id
    FROM
        {{ get_source('lake','dl_mysale_message') }}
)

SELECT *
FROM
    uniq
WHERE
    row_id = 1
