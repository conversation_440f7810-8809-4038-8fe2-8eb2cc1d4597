{{ config(
    materialized = 'incremental',
    partition_by = "ingestion_date",
    incremental_strategy= "insert_overwrite",
    file_format='delta',
    tags = [ "prod", "databricks", "gcp"]
) }}

SELECT DISTINCT
    c.adult,
    c.almostgonelimit,
    c.averagerating,
    brand:name AS brand,
    c.clubcatcheligible,
    c.clubcatchexclusive,
    c.clubcatchprice,
    c.discountpercent,
    c.eventid,
    c.freeshippingavailable,
    c.id,
    c.imagelocation,
    c.images,
    c.ispersonalisedproduct,
    c.offerid,
    c.pricedisplaytype,
    c.productpath,
    c.productskus,
    c.producturl,
    c.promotiondescription,
    c.quantityforsale,
    c.ratingcount,
    c.retailprice,
    c.savingslabel,
    c.selectedofferid,
    c.selectedofferprice,
    c.selectedofferquantity,
    c.selectedoffersellerid,
    c.sellprice,
    c.sellable,
    c.sellerid,
    c.showclubcatchprice,
    c.showfromlabel,
    c.showpromotionprice,
    c.showpromotiontimer,
    c.showretailprice,
    c.showsavingslabel,
    c.showshort<PERSON><PERSON><PERSON>,
    c.showwaslabel,
    c.sourceid,
    c.sourceplatform,
    c.sourcetype,
    c.title,
    c.url,
    c.wasprice,
    c.ranking,
    c.keyword,
    c.page,
    c.seller_name,
    c.capture_at,
{% if is_incremental() %}
    TO_DATE(FROM_UTC_TIMESTAMP(c.ingestion_time_utc, 'Australia/Melbourne')) AS ingestion_date
FROM {{ get_source("lake", "catch_ranking") }} AS c
WHERE c.capture_at IS NOT NULL AND c.id IS NOT NULL
{% else %}
    CAST({{ input_file_name_substring(104, 10) }} AS Date) AS ingestion_date
FROM {{ get_source("lake", "catch_ranking_full") }} AS c
    WHERE c.capture_at IS NOT NUll AND c.id IS NOT NUll
    {% if env_var('DBT_ENV', 'dev') == 'dev' %}
        AND c.capture_at >= DATE_SUB('2025-01-22', 30)
    {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
        AND c.capture_at >= DATE_SUB('2025-01-22', 90)
    {% endif %}
{% endif %}
