{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('na_product_combined_combo_var_filtered') }}
-- depends_on: {{ ref('na_product_combined_detail') }}
SELECT
    a.combined_sku,
    a.ean,
    a.created_at,
    COALESCE(a.common_listing, 'No Common SKU') AS common_listing,
    b.sku AS base_sku,
    IF(
        a.status = 1,
        'Enable',
        'Disable'
    ) AS status,
    IF(
        a.ab_kit = 1,
        TRUE,
        FALSE
    ) AS ab_kit,
    IF(
        COALESCE(b.qty, 0) = 0,
        1,
        b.qty
    ) AS qty_per_base
FROM
    {{ ref('na_product_combined_combo_var_filtered') }} AS a,
    {{ ref('na_product_combined_detail') }} AS b
WHERE
    a.id = b.product_combined_id
