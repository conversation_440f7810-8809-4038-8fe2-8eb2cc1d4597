{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = "id",
    partition_by = "ingestion_date",
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH raw_data AS (
    SELECT
        CONCAT_WS("-", sku, CAST(capture_at AS Date)) AS id,
        sku AS new_aim_active_sku,
        item_code AS level_1_code,
        live_on_site,
        CASE
            WHEN price_from = price_to THEN price_from
            ELSE CONCAT(price_from, " - ", price_to)
        END AS price,
        CAST(capture_at AS DATE) AS ingestion_date,
        link,
        capture_at
        {% if is_incremental() %}
        FROM {{ get_source("lake", "crawler_myer_health_check") }}
        {% else %}
    FROM {{ get_source("lake", "crawler_myer_health_check_full") }}
    {% endif %}
),

ranked_data AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY new_aim_active_sku, ingestion_date ORDER BY capture_at DESC) AS row_num
    FROM raw_data
)

SELECT
    id,
    new_aim_active_sku,
    level_1_code,
    live_on_site,
    price,
    link,
    ingestion_date,
    capture_at
FROM ranked_data
WHERE row_num = 1
