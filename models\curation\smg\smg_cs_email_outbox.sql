{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        --id,
        f_ref_id,
        f_sender,
        f_sent_date, ---unix
        ROW_NUMBER() OVER (PARTITION BY f_ref_id ORDER BY f_sent_date) AS row_id
    FROM
        {{ ref('dl_cs_email_outbox') }}
)

SELECT
    f_ref_id,
    f_sender,
    f_sent_date
FROM
    uniq
WHERE
    row_id = 1
