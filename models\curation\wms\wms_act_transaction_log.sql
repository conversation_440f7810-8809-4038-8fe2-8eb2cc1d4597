{{ config(
    materialized = 'incremental',
    unique_key='TransactionID',
    incremental_strategy='merge',
    partition_by = 'transaction_year',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH wms_act_transaction_log AS (
    {% if is_incremental() %}
        SELECT DISTINCT *
        FROM {{ get_source("lake","wms_act_transaction_log") }}
    {% else %}
    SELECT DISTINCT
        *   
    FROM {{ get_source("lake","wms_act_transaction_log_partition") }}
    UNION
    SELECT DISTINCT
        *        
    FROM {{ get_source("lake","wms_act_transaction_log_history") }}
    {% endif %}
)
,
the_newest_transaction AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY transactionid ORDER BY edittime DESC) AS newest_rank
    FROM wms_act_transaction_log
)

SELECT DISTINCT
    transactionid,
    transactiontype,
    doctype,
    TRIM(docno) AS docno,
    doclineno,
    status,
    fmcustomerid,
    fmsku,
    TRIM(fmlotnum) AS fmlotnum,
    fmlocation,
    fmid,
    fmpackid,
    fmuom,
    fmqty,
    fmqty_each,
    tocustomerid,
    tosku,
    tolotnum,
    tolocation,
    toid,
    topackid,
    touom,
    toqty,
    toqty_each,
    totalprice,
    totalnetweight,
    totalgrossweight,
    totalcubic,
    transactiontime,
    addtime,
    addwho,
    edittime,
    editwho,
    operator,
    warehouseid,
    YEAR(transactiontime) AS transaction_year
FROM the_newest_transaction
WHERE newest_rank = 1
