version: 2

models:
  - name: wms_act_transaction_log
    description: |
      A table to calculate daily outbound from the warehouse.
    columns:
      - name: TransactionID 
        tests:
          - not_null
          - unique
  - name: wms_doc_order_header
    description: |
      For WMS KPI use to see Warehouse staff performance
    columns:
      - name: OrderNo 
        tests:
          - not_null
          - unique
  - name: wms_stock_snapshot_current_day
    description: Current Base SKU Inventory with warehouse information, This table is a small table for wms_stocksnapshot records yesterday inventory.
    columns:
      - name: sku
        description: |
          base sku
        tests:
          - not_null
      - name: qtyonhold
        description: |
          Stock on hold by buyer, not for sale, but need to include this stock for bcg sow calculation
        tests:
          - not_null
      - name: snapshot_date
        description: |
          snapshot date for each sku stock record. normally thee snapshot_date is today -1
        tests:
          - not_null
      - name: primary_key
        description: |
          A combination key for incremental purpose, key = warehouseid + sku + yesterday
        tests:  
          - not_null
          - unique
      - name: warehouseid
        description: |
          some sku do have warehouse id , if warehouse if = WH9999, which means the source data does not have warehouse id.
        tests:
          - not_null
      - name: snapshot_year
        description: Partition column
        tests:
          - not_null

  - name: tmp_newaim_inventory_snapshot
    columns:
      - name: SnapShotDate
        description: |
          End of the day when WMS take a photo of inventory qty on that day
        tests:
          - not_null
      - name: sku
        description: |
          base sku
        tests:
          - not_null 
      - name: receivingdate
        description: |
          The date that warehouse recieved the SKU in a specific PO, a SKU may have different receiving date
        tests: 
          - not_null
      - name: cost
        description: |
          landing cost of the SKU for a specific Job No
      - name: qty
        description: |
          qty received
      - name: amount
        description: |
          cost multiplied by qty
      - name: warehouseid
        description: |
          warehouse id includes as per 16/08/2022:'AUSYDA,WH01,WH02,WH03,WH04,WH05,WH06,WH07,WH08,WHSP', and be aware that WHSP and AUSYDA is not newaim products
        tests:
          - not_null
      - name: primary_key
        description: Primary Key
        tests:
          - not_null
          - unique
      - name: snapshot_year
        description: Partition Column
        tests:
          - not_null
  - name: wms_doc_loading_header
    description: |
      FULL table to record the situation of warehouse staffs unload the carrier items every day
    columns:
      - name: LDLNO
        description: |
          primary key
        tests:
          - not_null
          - unique
      - name: Status
        description: |
          99 or 00
        tests:
          - not_null
      - name: VehicalNo
        description: |
          truck no
      - name: LoadingFromTime
        description: |
          loading start time
        tests:
          - not_null
      - name: LoadingToTime
        description: |
          loading end time
        tests:
          - not_null
      - name: WarehouseID
        description: |
          Warehouse code
        tests:
          - not_null
  - name: current_base_sku_stock
    description: |
      Source Talbe: inv_lot_att and multiview warehouse, This table group by to Base sku level,
      Exclude WHSP: Sapre Part Warehouse
    columns:
      - name: total_qty
        description: |
          qtytotal from inv_lot_att
        tests:
          - not_null
    columns:
      - name: stock
        description: |
          qtytotal - qtyallocated
        tests:
          - not_null
    columns:
      - name: available_qty
        description: |
          qtytotal - qtyallocated - qtyonhold
        tests:
          - not_null
    columns:
      - name: sku
        description: |
          Base SKU
        tests:
          - not_null
          - unique
  - name: inv_lot_att
    description: |
      Critical Table records SKU Stock and its location
    columns:
      - name: lotnum
        description: |
          Location Number, Primary key
        tests:
          - not_null
          - unique
      - name: sku
        description: Base SKU
        tests:
          - not_null
      - name: warehouseid
        description: Warehouse ID
        tests:
          - not_null
      - name: addtime
        description: System Generation Time
        tests:
          - not_null
      - name: edittime
        description: Edit Time, latest update
        tests:
          - not_null
      - name: add_year
        description: Partition Column
        tests:
          - not_null
  - name: bas_sku_full
    description: |
      This table include the historical data from WMS base sku
    columns:
      - name: sku
        description: base sku from WMS
        tests:
          - not_null
      - name: SKULength
        description: product length
        tests:
          - not_null
      - name: SKUWidth
        description: product width
        tests:
          - not_null
      - name: SKUHigh
        description: product high
        tests:
          - not_null
      - name: ingestion_time_utc
        description: snapshot time
        tests:
          - not_null
  - name: tmp_so_eparcel_label_full
    description: |
      This table include the historical data of parcel relationship with order
    columns:
      - name: eparcel_label_key
        description: |
          D_EDI_05 + Service + orderno, Primary key, provided by Donglin Zhang
        tests:
          - not_null
          - unique
      - name: orderno
        description: WMS order number
        tests:
          - not_null
      - name: d_edi_03
        description: base sku
        tests:
          - not_null
      - name: d_edi_05
        description: parcel number
        tests:
          - not_null
      - name: service
        description: courier name
        tests:
          - not_null
      - name: totalgrossweight
        description: sku gross weight
      - name: totalcubic
        description: sku cubic weight
      - name: print_flag
        description: Y or N
        tests:
          - not_null
      - name: pack_flag
        description: Y, N or W
        tests:
          - not_null
  - name: bas_package
    description: |
      WMS Per Pallet information, corresponding to bas sku from wms, 
    columns:
      - name: packid
        description: |
          Per Pallet Pack ID
        tests:
          - not_null
          - unique
      - name: descr
        description: pack ID description
        tests:
          - not_null
      - name: addtime
        descriptoin: Creation Time
        tests:
          - not_null
      - name: edittime
        descriptoin: latest Edit time
        tests:
          - not_null
      - name: ingestion_time_utc
        description: Data Factory Ingestion Time
        tests:
          - not_null
  - name: wh_asset_current_day
    description: |
      Capture the latest live status of all Assets in all warehouses (e.g. forklifts), as an optimal, central resource for all management and SH.
    columns:
      - name: asset_no
        description: |
          unique ID assigned to each asset
        tests:
          - not_null
          - unique
      - name: serial_number
        description: unique serial number for each asset
        tests:
          - not_null
      - name: location
        description: the WH where Asset is currently located
        tests:
          - not_null
      - name: make
        description: the make of the asset
        tests:
          - not_null
      - name: type_of_equipment
        description: the type of asset, fixed list
        tests:
          - not_null
      - name: status
        decription: fixed list (list might be modified in future by SH)
        tests:
          - not_null
      - name: ingestion_date
        description: update date
        tests:
          - not_null
