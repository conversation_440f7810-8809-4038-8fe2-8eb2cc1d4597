{{ config(
    materialized = 'view',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH unioned AS (
    SELECT *
    FROM {{ ref ("order_allocation_detail") }}
    UNION
    SELECT *
    FROM {{ ref ("order_allocation_detail_his") }}
),

unic AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY modified DESC) AS row_no
    FROM
        unioned
)

SELECT *
FROM
    unic
WHERE
    row_no = 1
