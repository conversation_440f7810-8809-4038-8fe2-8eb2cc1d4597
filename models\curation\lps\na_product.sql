{{ config(
    materialized = 'table',
    unique_key='sku',
    tags = [ "prod", "databricks", "gcp" ]
) }}


WITH unic AS (
    SELECT
        UPPER(TRIM(sku)) AS sku,
        MAX(created_at) AS created_at
    FROM
        {{ get_source('lake','na_product') }}
    WHERE
        sku NOT LIKE ('%PARENT%')
        AND
        sku != '---'
        AND
        status != 3 -- draft
        AND
        status != 0 -- deleted
    GROUP BY
        UPPER(TRIM(sku))
)
,
remove_dup AS (
    SELECT DISTINCT
        a.id,
        UPPER(TRIM(a.sku)) AS sku,
        a.name,
        a.package_name,
        a.barcode,
        a.combined,
        a.category_id,
        a.color,
        a.model,
        a.style,
        a.length,
        a.width,
        a.height,
        a.cbm,
        a.cubic_weight,
        a.net_weight,
        a.seasonal,
        a.indoor_outdoor,
        a.electrical_product,
        a.power_requirements,
        a.mandatory,
        a.status,
        a.creator_id,
        a.created_at,
        a.operator_id,
        a.operated_at,
        a.department_id,
        a.category_cn_name,
        -- Temporary solution for 2024-06-07 incidene; Need to change back to the best practice way later
        COALESCE(UPPER(a.category_en_name), 'No Value') AS category_en_name,
        a.creator_cn_name,
        a.creator_en_name,
        a.operator_cn_name,
        a.operator_en_name,
        a.department_cn_name,
        a.department_en_name,
        a.updated_at,
        a.new_product,
        a.age_limit,
        a.ean,
        a.is_sync,
        a.flag_first,
        a.qc_index,
        a.sync_time,
        a.purchase_type,
        a.flag_approve,
        a.handler_id,
        UPPER(TRIM(a.handler_cn_name)) AS handler_cn_name,
        COALESCE(UPPER(TRIM(a.handler_en_name)), 'No Handler') AS handler_en_name,
        a.to_asn,
        a.owner,
        a.palletized,
        a.is_forecast,
        a.brand_id,
        a.brand_name,
        a.flag_asn_passed,
        a.unloaded_date,
        a.discontinued,
        a.sku_status,
        a.manufacturing_mode,
        a.assistant_id,
        a.assistant_cn_name,
        a.assistant_en_name,
        a.replacement_product,
        a.ranking,
        a.similarity,
        a.has_inner_carton,
        a.category2,
        a.bcg,
        a.pallet_factor,
        a.is_common,
        a.status_hold_date,
        a.handler_department_en_name,
        a.conversion_date,
        a.lps_category_id,
        a.dangerous_good,
        a.predecessor_product,
        a.listing_special_request,
        a.listing_special_label,
        a.accc_furniture_category
    FROM
        {{ get_source('lake','na_product') }} AS a, unic AS b
    WHERE
        UPPER(TRIM(a.sku)) = b.sku
        AND
        a.created_at = b.created_at
)
,
style AS (
    SELECT
        sku,
        EXPLODE(SPLIT(COALESCE(style, 'No Material'), ',')) AS sku_style
    FROM remove_dup
)
,
compile_style AS (
    SELECT
        a.sku,
        COALESCE(b.en_name, a.sku_style) AS sku_style
    FROM style AS a
        LEFT JOIN {{ ref("na_product_material") }} AS b ON a.sku_style = b.id
)
,
combine_style AS (
    SELECT
        sku,
        ARRAY_JOIN(COLLECT_LIST(sku_style), ',') AS sku_style
    FROM compile_style
    GROUP BY sku
)
,
color AS (
    SELECT
        sku,
        EXPLODE(SPLIT(COALESCE(color, 'No Color'), ',')) AS color
    FROM remove_dup
)
,
compile_color AS (
    SELECT
        a.sku,
        COALESCE(b.color, a.color) AS color
    FROM color AS a
        LEFT JOIN {{ ref("product_color_dictionary") }} AS b ON a.color = b.id
)
,
combine_color AS (
    SELECT
        sku,
        ARRAY_JOIN(COLLECT_LIST(color), ',') AS color
    FROM compile_color
    GROUP BY sku
)

SELECT
    a.* EXCEPT (style, color),
    b.sku_style AS `style`,
    c.color
FROM remove_dup AS a
    LEFT JOIN combine_style AS b ON a.sku = b.sku
    LEFT JOIN combine_color AS c ON a.sku = c.sku
