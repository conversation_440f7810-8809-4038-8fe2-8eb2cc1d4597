{{ config(
    materialized='incremental',
    unique_key = 'job_number',
    incremental_strategy = "merge",
    partition_by = 'modified_year',
    tags = [ "prod", "databricks", "gcp" ]
) }}

-- depends_on: {{ ref('netsuite_purchase_order_raw') }}
-- this is a fundermental table that will affect most of the tables related to NS, so the filters here will have a large impact
-- pls pay attention in adjusting the filters here
-- keep an eye on any business adjustment on the fields we used to filter

WITH po_customfield AS (
    SELECT
        tranid AS tranid_1,
        itemlist,
        custbody21,
        custbody_acs_buyer_assign_email AS buyer_assign_email,
        --noqa: disable=PRS
        SUBSTRING(custbody_acs_buyer_assignee :name, 4, 99) AS buyer_assignee,
        --noqa: enable=all
        custbody_acs_cust_doc_num AS custom_document_number,
        custbody_acs_deposit_amount AS deposit_amount,
        --noqa: disable=PRS
        custbody_acs_na_created_by :name AS acs_creator,
        custbody_acs_po_final_appr :name AS final_approver,
        --noqa: enable=all
        custbody_acs_purchase_approved AS purchase_approved,
        custbody_acs_submit_approval AS submit_approval,
        --noqa: disable=PRS
        custbody_atlas_no_hdn :name AS atlas_no_hdn,
        custbody_atlas_yes_hdn :name AS atlas_yes_hdn,
        --noqa: enable=all
        custbody_cash_register AS cash_register,
        --noqa: disable=PRS
        custbody_na_buyerteam :name AS buyer_team,
        custbody_na_contractportofdest :name AS contract_port_of_destination,
        custbody_na_contractportoforigin :name AS contract_port_of_origin,
        --noqa: enable=all
        custbody_na_depositrate AS deposit_rate,
        to_date(FROM_UTC_TIMESTAMP(custbody_na_orderetd, 'Australia/Melbourne')) AS expected_shipping_date_esd,
        custbody_na_paymentperiods AS payment_period_days,
        to_date(FROM_UTC_TIMESTAMP(custbody_na_readydate, 'Australia/Melbourne')) AS est_stock_ready_date,
        --noqa: disable=PRS
        custbody_na_shipmenttype :name AS shipment_type,
        custbody_na_shippingmethod :name AS shipping_method,
        --noqa: enable=all
        custbody_na_totalcbmvolume AS total_cbm,
        custbody_nondeductible_processed AS nondeductible_processed,
        custbody_report_timestamp AS report_timestamp,
        custbody_stc_amount_after_discount AS amount_after_discount,
        custbody_stc_tax_after_discount AS tax_after_discount,
        custbody_stc_total_after_discount AS total_after_discount,
        custbody_total_qty_ordered AS total_qty_ordered,
        custbody_total_qty_received AS total_qty_received,
        custbody_sii_not_reported_in_time,
        custbody_sii_article_72_73,
        custbodymo,
        custbodypayment_term,
        to_date(FROM_UTC_TIMESTAMP(custbodypo_eta, 'Australia/Melbourne')) AS custbodypo_eta,
        custbodypayment_term_days,
        --noqa: disable=PRS
        custbody_acs_po_preferred_bank:name AS Preferred_bank,
        --noqa: enable=all
        custbody_acs_overseas_bank,
        custbodyps_po_id,
        custbody_na_ordertitle,
        custbodyps_po_job_no,
        custbodyps_po_title,
        custbody_acs_reject_reason,
        custbodypo_etd,
        custbody_document_date,
        custbody11,
        --noqa: disable=PRS
        custbody_na_qcstatus:name AS QC_Status,
        --noqa: enable=all
        custbody_na_deposit_paymentdate,
        to_date(FROM_UTC_TIMESTAMP(custbodypo_eta_2, 'Australia/Melbourne')) AS expected_delivery_date_edd,
        custbody_na_ship_via_lps,
        custbody_na_doc_uploaded
    FROM (
        SELECT
            tranid,
            itemlist,
            INLINE(
                FROM_JSON(
                    --noqa: disable=PRS
                    customfieldlist :customfield [*],
                    'array<struct<value:string,scriptId:string>>'
                --noqa: enable=all
                )
            ) AS (value, customfieldname)
        FROM
            {{ ref('netsuite_purchase_order_raw') }}
        {% if is_incremental() %}
            WHERE
                FROM_UTC_TIMESTAMP(lastmodifieddate, 'Australia/Melbourne') >= (
                    SELECT MAX(modified_aest)
                    FROM
                        {{ this }}
                    WHERE
                        modified_year >= YEAR(CURRENT_DATE()) - 1
                )
        {% endif %}
    /*this is a filter to remove all the unnecessary transactions like those from Finance, DSZ or other departments,
    When we look at a specific transaction number in NS front end, as long as there is a column called [BARCODE] in the 'items' section,
    that means this transaction number is a purchase order from the buying team. Otherwise, it's other types of orders from other teams*/
    ) PIVOT (
            MAX(value) FOR customfieldname IN (
                'custbody21',
                'custbody_acs_buyer_assign_email',
                'custbody_acs_buyer_assignee',
                'custbody_acs_cust_doc_num',
                'custbody_acs_deposit_amount',
                'custbody_acs_na_created_by',
                'custbody_acs_po_final_appr',
                'custbody_acs_purchase_approved',
                'custbody_acs_submit_approval',
                'custbody_acs_vendor_select',
                'custbody_atlas_no_hdn',
                'custbody_atlas_yes_hdn',
                'custbody_cash_register',
                'custbody_na_buyerteam',
                'custbody_na_contractportofdest',
                'custbody_na_contractportoforigin',
                'custbody_na_depositrate',
                'custbody_na_orderetd',
                'custbody_na_paymentperiods',
                'custbody_na_readydate',
                'custbody_na_shipmenttype',
                'custbody_na_shippingmethod',
                'custbody_na_totalcbmvolume',
                'custbody_nondeductible_processed',
                'custbody_report_timestamp',
                'custbody_sii_article_72_73',
                'custbody_sii_not_reported_in_time',
                'custbody_stc_amount_after_discount',
                'custbody_stc_tax_after_discount',
                'custbody_stc_total_after_discount',
                'custbody_total_qty_ordered',
                'custbody_total_qty_received',
                'custbodymo',
                'custbodypo_eta_2',
                'custbodypayment_term',
                'custbodypo_eta',
                'custbodypayment_term_days',
                'custbody_acs_po_preferred_bank',
                'custbody_acs_overseas_bank',
                'custbodyps_po_id',
                'custbody_na_ordertitle',
                'custbodyps_po_job_no',
                'custbodyps_po_title',
                'custbody_acs_reject_reason',
                'custbodypo_etd',
                'custbody_document_date',
                'custbody11',
                'custbody_na_qcstatus',
                'custbody_na_deposit_paymentdate',
                'custbody_na_ship_via_lps',
                'custbody_na_doc_uploaded'
            )
    )
),

po AS (
    SELECT
        tranid AS job_number,
        status AS job_status,
        TO_DATE(FROM_UTC_TIMESTAMP(createddate, 'Australia/Melbourne')) AS created_date,
        TO_DATE(FROM_UTC_TIMESTAMP(trandate, 'Australia/Melbourne')) AS trandate,
        currencyname AS currency,
        exchangerate,
        externalid,
        FROM_UTC_TIMESTAMP(lastmodifieddate, 'Australia/Melbourne') AS modified_aest,
        subtotal,
        taxtotal,
        total AS total_amount,
        --noqa: disable=PRS
        terms :name AS terms,
        incoterm :name AS inncoterm,
        entity :internalId AS vendor_id,
        entity :name AS vendor_name,
        SUBSTRING(employee :name, 4, 99) AS buyer,
        approvalstatus :name AS approvalstatus,
        SUBSTRING(NextApprover :name, 4, 99) AS nextapprover
    --noqa: enable=all
    FROM
        {{ ref('netsuite_purchase_order_raw') }}
    WHERE
        {% if is_incremental() %}
            FROM_UTC_TIMESTAMP(lastmodifieddate, 'Australia/Melbourne') >= (
                SELECT MAX(modified_aest)
                FROM
                    {{ this }}
                WHERE
                    modified_year >= YEAR(CURRENT_DATE()) - 1
            )
            AND
        {% endif %}
        (
            (
                --noqa: disable=PRS
                entity :name <> 'VD2043 Testing Vendor for MVP' --this filter is to remove the test orders from the BA team
            --noqa: enable=all
                AND itemlist LIKE '%custcol_acs_item_barcode%' -- the POs created in NS naturally
            )
            OR customfieldlist LIKE '%custbody_na_ship_via_lps%' -- the POs created in LPS
        )
/*this is a filter to remove all the unnecessary transactions like those from Finance, DSZ or other departments,
When we look at a specific transaction number in NS front end, as long as there is a column called [BARCODE] in the 'items' section,
that means this transaction number is a purchase order from the buying team. Otherwise, it's other types of orders from other teams*/
)

SELECT
    po.*,
    --Disable parsing error for the rest of file
    --noqa: disable=PRS
    b.* EXCEPT(b.tranid_1),
    --noqa: enable=all
    YEAR(modified_aest) AS modified_year
FROM
    po
    LEFT JOIN po_customfield AS b ON po.job_number = b.tranid_1
