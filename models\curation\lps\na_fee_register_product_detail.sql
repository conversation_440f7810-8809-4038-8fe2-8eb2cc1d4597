{{ config(
    materialized = 'incremental',
    incremental_strategy='merge',
    unique_key = 'primary_key',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT DISTINCT
    order_id,
    business_id,
    CASE
        WHEN currency = 1 THEN 'AUD'
        WHEN currency = 2 THEN 'RMB'
        ELSE 'USD'
    END AS currency,
    price_aud,
    price_rmb,
    price_usd,
    sku,
    CONCAT(
        id,
        '-',
        business_id
    ) AS primary_key
FROM
    {{
        get_source(
            "lake",
            "na_fee_register_product_detail"
        )
    }}
