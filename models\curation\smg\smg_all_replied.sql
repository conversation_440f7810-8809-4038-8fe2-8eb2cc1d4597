{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH unioned AS (
    --Catch--
    SELECT
        a.order_id AS reference,
        a.user_id,
        a.role_,
        a.created_aest,
        IF(b.catch_id IS NOT NULL, TRUE, FALSE) AS is_ignore,
        'Catch' AS channel
    FROM
        {{ ref('smg_catch_message') }} AS a
        LEFT JOIN
            {{ ref('dl_catch_message_log') }} AS b
            ON
                a.catch_id = b.catch_id
    UNION
    -----------Bunnings--------------
    SELECT
        a.order_id AS reference,
        a.user_id,
        a.role_,
        a.created_aest,
        IF(b.bunnings_id IS NOT NULL, TRUE, FALSE) AS is_ignore,
        'Bunnings' AS channel
    FROM
        {{ ref('smg_bunnings_message') }} AS a
        LEFT JOIN
            {{ ref('dl_bunnings_message_log') }} AS b
            ON
                a.bunnings_id = b.bunnings_id
    WHERE
        a.order_id LIKE 'W%'
    UNION
    -----------Kogan-------------
    SELECT
        a.ticket_id AS reference,
        a.user_id,
        a.role_,
        a.created_aest,
        IF(b.message_id IS NOT NULL, TRUE, FALSE) AS is_ignore,
        'Kogan' AS channel
    FROM
        {{ ref('smg_kogan_message') }} AS a
        LEFT JOIN
            {{ ref('dl_kogan_message_log') }} AS b
            ON
                a.message_id = b.message_id
    UNION
    --------------Mydeal---------------
    SELECT
        a.ticket_id AS reference,
        a.user_id,
        a.role_,
        a.created_aest,
        IF(b.message_id IS NOT NULL, TRUE, FALSE) AS is_ignore,
        'Mydeal' AS channel
    FROM
        {{ ref('smg_mydeal_message') }} AS a
        LEFT JOIN
            {{ ref('dl_mydeal_message_log') }} AS b
            ON
                a.message_id = b.message_id
    UNION
    ----------Mysale--------------
    SELECT
        a.ticket_id AS reference,
        a.user_id,
        a.role_,
        a.created_aest,
        IF(b.mysale_id IS NULL, TRUE, FALSE) AS is_ignore,
        'Mysale' AS channel
    FROM
        {{ ref('smg_mysale_message') }} AS a
        LEFT JOIN
            {{ ref('dl_mysale_message_log') }} AS b
            ON
                a.message_id = b.mysale_id
    UNION
    -----------DSZ-----------
    SELECT
        a.order_id AS reference,
        a.user_id,
        a.role_,
        a.created_aest,
        IF(b.dsz_id IS NULL, TRUE, FALSE) AS is_ignore,
        'DSZ' AS channel
    FROM
        {{ ref('smg_dsz_message') }} AS a
        LEFT JOIN
            {{ ref('dl_dsz_message_log') }} AS b
            ON
                a.dsz_id = b.dsz_id
)

SELECT *
FROM
    unioned
