{{ config(
    materialized = 'table',
    unique_key = 'id' ,
    tags = [ "prod", "databricks", "gcp" ]
) }}

---order_no without shipment time are dynamic may be cancelled

WITH base_sku_revenue AS (
    SELECT
        a.id,
        a.tid,
        a.order_no,
        a.customer_order_id,
        a.order_status,
        a.copy_order_status,
        a.order_time,
        a.order_year,
        a.delivery_time AS shipment_time,
        a.delivered_time,
        a.sku,
        a.store,
        a.is_swap_order,
        a.express_mode,
        a.qty,
        a.subtotal,
        CASE
            WHEN COALESCE(a.unit_cost, 0) = 0 THEN COALESCE(a.cost_, 0)
            ELSE a.unit_cost
        END AS unit_cost,
        ----WSNS is oversea customer no need tax for australia government 
        ---------------------------------------------------------------
        CASE
            WHEN UPPER(a.store) IN ('WSNS', 'NZHNDS')
                THEN a.unit_price
            WHEN a.currency = 'NZ'
                THEN a.unit_price / 1.15
            ELSE a.unit_price / 1.1
        END AS unit_price_ex_gst,
        a.unit_price,
        COALESCE(a.postage_charged, 0) / 1.1
        AS postage_charged_ex_gst,

        ----WSNS is oversea customer no need tax for australia government
        CASE
            WHEN UPPER(a.store) = 'WSNS'
                THEN (CASE
                    WHEN UPPER(a.express_mode) = 'NOCARRIER' THEN COALESCE(a.postage_charged, 0)
                    WHEN COALESCE(a.post_est, 0) = 0 THEN COALESCE(a.initial_post_est, 0)
                    ELSE a.post_est
                END)
            ELSE (CASE
                WHEN UPPER(a.express_mode) = 'NOCARRIER'
                    THEN (COALESCE(a.postage_charged, 0) / CAST(1.1 AS DEC(18, 4)))
                WHEN COALESCE(a.post_est, 0) = 0
                    THEN (COALESCE(a.initial_post_est, 0) / CAST(1.1 AS DEC(18, 4)))
                ELSE (a.post_est / CAST(1.1 AS DEC(18, 4)))
            END)
        END AS postage_cost_ex_gst,
        a.user_nick,
        a.consignee,
        a.phone,
        a.mobile,
        a.address,
        a.state_,
        a.suburb,
        a.zipcode,
        a.job_no,
        a.express_no,
        a.warehouse_id,
        a.currency,
        a.ismetro,
        a.postage_estimation,
        a.postage_estimation_map,
        a.remoteness,
        --Split Order Status From OMS Order Header, Whaen split order is not 0 then the original order splits into two or more orders
        a.is_split_order
    FROM {{ ref ("oms_sales_90days") }} AS a
        LEFT JOIN {{ ref("order_header_unioned") }} AS b ON a.order_no = b.order_no
    WHERE UPPER(b.remark) != 'TEST'     -- This logic is confirmed by Grant Fan. Those orders have no business value
)
,
amazon_cate_commission AS (
    SELECT
        a.sku,
        a.subcategory_lv3,
        b.amz_cate_commission
    FROM {{ ref("base_sku_attributes") }} AS a
        LEFT JOIN {{ ref("amz_mapping_for_fee") }} AS b ON a.subcategory_lv3 = b.l3
),

base_sku_revenue_with_base_measures AS (
    SELECT
        a.*,
        CASE
            WHEN a.is_swap_order = 'N' OR a.is_swap_order IS NULL
                THEN
                    CASE
                        WHEN a.store IN ('WSNS', 'NZHNDS') THEN a.subtotal
                        WHEN a.currency = 'NZ' THEN a.subtotal / 1.15
                        ELSE a.subtotal / 1.1
                    END + COALESCE(a.postage_charged_ex_gst, 0)
            ELSE 0
        END
        AS sales_ex_gst_in_different_currency,
        (CASE
            WHEN a.is_swap_order = 'N' OR a.is_swap_order IS NULL
                THEN
                    CASE
                        WHEN a.store IN ('WSNS', 'NZHNDS') THEN a.subtotal + COALESCE(a.postage_charged_ex_gst, 0)
                        WHEN a.currency = 'NZ' THEN a.subtotal / 1.15 + COALESCE(a.postage_charged_ex_gst, 0)
                        WHEN a.store IN ('AMZP', 'AMZFC', 'AMZN') THEN a.subtotal
                        ELSE a.subtotal / 1.1 + COALESCE(a.postage_charged_ex_gst, 0)
                    END
            ELSE 0
        END)
        * CASE
            -- This reference column is coming from the column "Estimated Standard Referral Fee %" in AMZNFC&AMZP Commission Googlesheet
            WHEN a.store IN ('AMZP', 'AMZFC') THEN COALESCE(c.est_discount_fee, d.amz_cate_commission, 0.10)
            -- This reference column is coming from the column "Estimated Discounted Referral Fee %" in AMZNFC&AMZP Commission Googlesheet
            WHEN a.store IN ('AMZN') THEN COALESCE(c.est_discounted_referral_fee, d.amz_cate_commission, 0.13)
            ELSE COALESCE(cm.commission, 0)
        END
        AS commission_in_different_currency,
        a.qty * a.unit_cost AS cogs
    FROM
        base_sku_revenue AS a
        LEFT JOIN
            {{ ref ("OMS_channel_price_management") }} AS cm
            ON
                a.order_time >= cm.active_time AND a.order_time < cm.end_time AND a.store = cm.bbcode
        LEFT JOIN {{ ref("amznfc_amzp_commission_scd") }} AS c
            ON a.order_time >= c.valid_from AND a.order_time < c.valid_to AND a.sku = c.sku
        LEFT JOIN amazon_cate_commission AS d
            ON a.sku = d.sku
)
,
base_sku_revenue_with_extral_measures AS (
    SELECT
        a.*,
        CASE
            WHEN a.currency = 'NZ' THEN CAST(a.sales_ex_gst_in_different_currency / 1.11 AS DOUBLE)
            WHEN a.currency = 'US' THEN CAST(a.sales_ex_gst_in_different_currency * 1.55 AS DOUBLE)
            WHEN a.currency = 'CN' THEN CAST(a.sales_ex_gst_in_different_currency * 0.214 AS DOUBLE)
            ELSE CAST(a.sales_ex_gst_in_different_currency AS DOUBLE)
        END AS sales_ex_gst,
        CASE
            WHEN a.currency = 'NZ' THEN CAST(a.commission_in_different_currency / 1.11 AS DOUBLE)
            WHEN a.currency = 'US' THEN CAST(a.commission_in_different_currency * 1.55 AS DOUBLE)
            WHEN a.currency = 'CN' THEN CAST(a.commission_in_different_currency * 0.214 AS DOUBLE)
            ELSE CAST(a.commission_in_different_currency AS DOUBLE)
        END AS commission
    FROM
        base_sku_revenue_with_base_measures AS a
)

SELECT
    a.*,
    a.sales_ex_gst - COALESCE(a.commission, 0) - COALESCE(a.cogs, 0) - COALESCE(a.postage_cost_ex_gst, 0) AS profit
FROM
    base_sku_revenue_with_extral_measures AS a
