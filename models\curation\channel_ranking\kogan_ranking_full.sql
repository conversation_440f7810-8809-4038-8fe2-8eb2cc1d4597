{{ config(
    materialized = 'incremental',
    unique_key = "unique_id",
    incremental_strategy= "merge",
    partition_by = "ingestion_date",
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH ranking AS (
    SELECT
        SPLIT(product_link, '\\?')[0] AS product_link,
        promotion_price,
        original_price,
        product_title,
        ranking,
        page,
        page_rank,
        keyword,
        image_url,
        is_sponsored,
        rating,
        review_count,
        response_status,
        capture_at,
        TO_DATE(capture_at) AS capture_date,
        ingestion_date
    FROM {{ get_source("crawler_delta_lake", "kogan_ranking_full") }}
    WHERE
        ranking IS NOT NULL
        {% if is_incremental() %}
            AND ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
        {% else %}
            {% if env_var('DBT_ENV', 'dev') == 'dev' %}
                AND ingestion_date >= date_sub(current_date(), 15)
            {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
                AND ingestion_date >= date_sub(current_date(), 60)
            {% endif %}
        {% endif %}
)
,
details AS (
    SELECT
        product_url,
        brand,
        non_member_price,
        seller,
        capture_at AS detail_capture_at,
        TO_DATE(capture_at) AS detail_capture_date
    FROM {{ get_source("crawler_delta_lake", "kogan_detail_full") }}
    {% if is_incremental() %}
        WHERE ingestion_date >= (SELECT MAX(detail_capture_date) FROM {{ this }})
    {% else %}
      {% if env_var('DBT_ENV', 'dev') == 'dev' %}
          WHERE ingestion_date >= date_sub(current_date(), 15)
      {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
          WHERE ingestion_date >= date_sub(current_date(), 60)
      {% endif %}
  {% endif %}
)
,
details_ranked AS (
    -- Get detail record where detail_capture_at <= capture_at (if any)
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY product_url, capture_at
            ORDER BY detail_capture_at DESC
        ) AS rn
    FROM (
        SELECT
            a.*,
            b.*
        FROM ranking AS a
            LEFT JOIN details AS b
                ON
                    a.product_link = b.product_url
                    AND b.detail_capture_at <= a.capture_at
    )
),

details_earliest_find AS (
    -- Fallback to earliest detail for product (in case no earlier detail exists)
    SELECT
        *,
        ROW_NUMBER() OVER (
            PARTITION BY product_url
            ORDER BY detail_capture_at
        ) AS rn
    FROM details
),

details_earliest AS (
    SELECT * FROM details_earliest_find WHERE rn = 1
)
,
final_joined AS (
    SELECT
        r.product_link,
        r.promotion_price,
        r.original_price,
        r.product_title,
        r.ranking,
        r.page,
        r.page_rank,
        r.keyword,
        r.image_url,
        r.is_sponsored,
        r.rating,
        r.review_count,
        r.response_status,
        r.capture_at,
        r.capture_date,
        r.ingestion_date,
        COALESCE(r.brand, e.brand) AS brand,
        COALESCE(r.non_member_price, e.non_member_price) AS non_member_price,
        COALESCE(r.seller, e.seller) AS seller,
        COALESCE(r.detail_capture_at, e.detail_capture_at) AS detail_capture_at,
        COALESCE(r.detail_capture_date, e.detail_capture_date) AS detail_capture_date
    FROM (
        SELECT * FROM details_ranked WHERE rn = 1
    ) AS r
        LEFT JOIN details_earliest AS e ON r.product_link = e.product_url
)
,
daily_latest_capture AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY product_link, capture_date ORDER BY capture_at DESC) AS rn
    FROM final_joined
)

SELECT
    product_link,
    promotion_price,
    original_price,
    product_title,
    ranking,
    page,
    page_rank,
    keyword,
    image_url,
    is_sponsored,
    rating,
    review_count,
    response_status,
    capture_at,
    capture_date,
    ingestion_date,
    brand,
    non_member_price,
    seller,
    detail_capture_at,
    detail_capture_date,
    CONCAT(product_link, '-', keyword, '-', capture_date) AS unique_id
FROM daily_latest_capture
WHERE rn = 1
