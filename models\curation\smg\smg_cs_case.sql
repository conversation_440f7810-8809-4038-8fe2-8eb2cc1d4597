{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        FROM_UTC_TIMESTAMP(FROM_UNIXTIME(f_create_date), 'Australia/Melbourne') AS create_time_aest,
        IF(f_is_ignore = 1, TRUE, FALSE) AS is_ignore,
        COALESCE(f_respond_by_date, f_last_modify_date) AS watermark,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(f_respond_by_date, f_last_modify_date) DESC) AS row_id
    FROM
        {{ ref('dl_cs_case') }}
)

SELECT *
FROM
    uniq
WHERE
    row_id = 1
