{{ config(
    materialized = 'incremental',
    partition_by = "ingestion_date",
    incremental_strategy= "insert_overwrite",
    file_format='parquet' if not check_env_is_gcp() else 'delta',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH mydeal_store_sku_full AS (
    SELECT DISTINCT product_sku
    FROM {{ ref ("mydeal_store_sku_full") }}
)

SELECT ranking.*
FROM {{ ref ("mydeal_ranking_full") }} AS ranking
    LEFT JOIN mydeal_store_sku_full AS market_sku
        ON ranking.product_sku = market_sku.product_sku
WHERE market_sku.product_sku IS NOT NULL
