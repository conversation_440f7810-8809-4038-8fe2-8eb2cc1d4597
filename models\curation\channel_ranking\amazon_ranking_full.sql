{{ config(
    materialized = 'incremental',
    unique_key = "unique_id",
    incremental_strategy= "merge",
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    r.search_url,
    r.keyword,
    r.page,
    r.ranking,
    CASE WHEN r.page > 1 THEN (r.page - 1) * 60 + r.ranking ELSE r.ranking END AS overall_ranking,
    r.url,
    r.asin,
    r.product_name,
    r.sold_by_price,
    r.rating,
    r.sponsored,
    r.is_prime,
    r.limited_deal,
    r.save_with_coupon,
    r.coupon_discount,
    r.amazon_choice,
    r.best_seller,
    r.freeshipping,
    r.image_url,
    r.capture_at,
    r.response_status,
    r.ships_from,
    r.ingestion_date,
    r.unique_id,
    d.seller_name,
    d.brand_name,
    d.product_info,
    d.product_dimensions,
    d.item_weight,
    d.material,
    d.best_sellers_rank,
    d.detail_update_at,
    COALESCE(LOWER(d.seller_name) = "artiss furnishings", FALSE) AS na_product
FROM {{ ref("amazon_ranking_raw") }} AS r LEFT JOIN {{ ref("amazon_product_detail_full") }} AS d ON r.asin = d.asin
{% if is_incremenatal %}
    WHERE r.page IS NOT NULL AND r.ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
{% else %}
    WHERE r.page IS NOT NULL
    {% if env_var('DBT_ENV', 'dev') == 'dev' %}
        AND r.ingestion_date >= DATE_SUB(current_date(), 20)
    {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
        AND r.ingestion_date >= DATE_SUB(current_date(), 60)
    {% endif %}
{% endif %}
