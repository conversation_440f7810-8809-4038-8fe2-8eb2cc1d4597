{{ config(
    materialized = 'view',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH unioned AS (
    SELECT *
    FROM {{ ref ("order_detail") }}
    UNION
    SELECT *
    FROM {{ ref ("order_detail_his") }}
    WHERE
        modified < (SELECT MIN(modified) FROM {{ ref('order_detail') }})
        OR
        modified > (SELECT MAX(modified) FROM {{ ref('order_detail') }})
),

unic AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY modified DESC) AS row_id
    FROM
        unioned
)

SELECT *
FROM
    unic
WHERE
    row_id = 1
