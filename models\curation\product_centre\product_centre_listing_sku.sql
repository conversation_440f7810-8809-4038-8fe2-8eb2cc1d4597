{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('product_centre_listing_sku_raw') }}
-- TODO: CHANGE THIS MODEL to another name: "product_center_listing_sku_for_price_review" 
-- WARNING: This table only contain "active" and "In Progress" SKU
-- PLEASE use table: "product_centre_listing_sku_raw" to get FULL list of Product Center's Listing SKUs

SELECT *
FROM {{ ref ('product_centre_listing_sku_raw') }}
WHERE
    is_parent = 'No'
    AND sku IS NOT NULL
    --- Status Filter---
    AND status IN (5, 6)
