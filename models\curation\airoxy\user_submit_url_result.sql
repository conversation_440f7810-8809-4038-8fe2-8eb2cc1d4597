{{ config(
        materialized = "incremental",
        unique_key = "id",
        incremental_strategy = "merge",
        tags = [ "prod", "databricks", "gcp" ]
) }}

WITH crawled AS (
    SELECT
        id,
        title,
        CAST(REGEXP_REPLACE(price, '/ea|,|\\$', '') AS DOUBLE) AS price,
        url,
        url_hash,
        image,
        capture_date,
        capture_time,
        CASE
            WHEN url LIKE '%ebay.com%' THEN 'www.ebay.com.au'
            WHEN url LIKE '%woolworths.com.au%' THEN 'www.woolworths.com.au'
            WHEN url LIKE '%bigw.com.au%' THEN 'www.bigw.com.au'
            WHEN url LIKE '%bunnings.com.au%' THEN 'www.bunnings.com.au'
            WHEN url LIKE '%amazon.com%' THEN 'www.amazon.com.au'
            WHEN url LIKE '%catch.com.au%' THEN 'www.catch.com.au'
            WHEN url LIKE '%mydeal.com.au%' THEN 'www.mydeal.com.au'
            WHEN url LIKE '%myer.com.au%' THEN 'www.myer.com.au'
        END AS channel
    FROM {{ ref('user_submit_url_result_raw') }}
    WHERE
        price IS NOT NULL AND response_status = 200
        AND url IS NOT NULL AND url_hash IS NOT NULL AND LEN(REGEXP_EXTRACT(price, '(\\d+\\.?\\d*)', 0)) > 0
        {% if is_incremental() %}
            AND capture_date >= (SELECT MAX(capture_date) FROM {{ this }})
        {% endif %}
),

-- Reason: For filtered out the null url and url_hash
-- The crawler it self is to fetch data from all urls in the model 'curation.user_submit_url_list' 
-- and store the result in 'crawler_lake.user_submit_url_result'
-- However, the crawler sometimes encounter error when fetching the data, so there will be empty lines in the csv and parquet files.
-- After inspect, the empty lines should be some url in the url_list, but for some reason the crawler cannot find url in the metadata of the requets.
-- However, these missing urls could be recovered from the later crawling, so we could just filter out the missing rows.

user_submit AS (
    SELECT DISTINCT
        url,
        url_hash
    FROM {{ ref("user_submit_url") }}
),

ranking_url AS (
    SELECT DISTINCT
        item_url AS `url`,
        title,
        sold_price AS price,
        image_url AS `image`,
        ingestion_date AS capture_date,
        capture_at AS capture_time,
        CASE
            WHEN platform LIKE '%ebay%' THEN 'www.ebay.com.au'
            WHEN platform LIKE '%woolworths%' THEN 'www.woolworths.com.au'
            WHEN platform LIKE '%bigw%' THEN 'www.bigw.com.au'
            WHEN platform LIKE '%bunnings%' THEN 'www.bunnings.com.au'
            WHEN platform LIKE '%amazon%' THEN 'www.amazon.com.au'
            WHEN platform LIKE '%catch' THEN 'www.catch.com.au'
            WHEN platform LIKE '%mydeal%' THEN 'www.mydeal.com.au'
            WHEN platform LIKE '%myer%' THEN 'www.myer.com.au'
        END AS channel,
        ROW_NUMBER() OVER (PARTITION BY item_url ORDER BY capture_at DESC) AS row_num
    FROM
        {{ ref("crawler_products") }}
    WHERE ingestion_date >= DATE_SUB(CURRENT_DATE(), 1)
),

existing_url AS (
    SELECT
        CONCAT_WS('-', u.url, r.capture_date) AS id,
        r.title,
        r.price,
        u.url,
        u.url_hash,
        r.image,
        r.capture_date,
        r.capture_time,
        r.channel
    FROM user_submit AS u INNER JOIN ranking_url AS r ON u.url = r.url
    WHERE r.row_num = 1
),

result AS (
    SELECT * FROM crawled
    UNION ALL
    SELECT * FROM existing_url
),

rmv_dup AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY url ORDER BY capture_date DESC) AS row_num
    FROM result
)

SELECT * EXCEPT (row_num) FROM rmv_dup WHERE row_num = 1 AND price IS NOT NULL AND LEN(REGEXP_EXTRACT(price, '(\\d+\\.?\\d*)', 0)) > 0
