{{ config(
    materialized='incremental',
    unique_key="id",
    incremental_strategy="merge", 
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH unic AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_timestamp DESC) AS row_no
    FROM
        {{ get_source("amazon_api_lake", "amazon_click_report") }}
    {% if is_incremental() %}
        WHERE report_date >= (SELECT DATE_SUB(MAX(report_date), 7) FROM {{ this }})
    {% endif %}
)


SELECT
    id,
    report_id,
    asin,
    impressions_count,
    impressions_median_price,
    impressions_same_day_shipping_count,
    impressions_1d_shipping_count,
    impressions_2d_shipping_count,
    clicks_count,
    clicks_click_rate,
    clicks_median_price,
    clicks_same_day_shipping_count,
    clicks_1d_shipping_count,
    clicks_2d_shipping_count,
    cart_adds_count,
    cart_adds_median_price,
    cart_adds_same_day_shipping_count,
    cart_adds_1d_shipping_count,
    cart_adds_2d_shipping_count,
    purchases_count,
    purchases_search_traffic_sales,
    purchases_conversion_rate,
    purchases_median_price,
    purchases_same_day_shipping_count,
    purchases_1d_shipping_count,
    purchases_2d_shipping_count,
    report_date
FROM unic
WHERE row_no = 1
