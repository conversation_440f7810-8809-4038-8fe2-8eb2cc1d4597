{{ config(
    materialized='table',
    indexes=[{'columns': ['order_number']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        order_id,
        order_number,
        status,
        product_id,
        UPPER(TRIM(sku)) AS sku,
        order_qty,
        packing_qty,
        currency,
        total_price_aud,
        price_cost_aud,
        price_cost_rmb,
        price_cost_usd,
        port_fee_aud,
        charge_item_fee_aud,
        tariff_aud,
        custom_processing_fee_aud,
        other_fee_aud,
        sub_cost_aud,
        total_price_cost_aud,
        total_port_fee_aud,
        total_charge_item_fee_aud,
        total_tariff_aud,
        total_custom_processing_fee_aud,
        total_other_fee_aud,
        total_sub_cost_aud,
        electronic_processing_fee_aud,
        ROW_NUMBER() OVER (
            PARTITION BY CONCAT(
                order_number,
                '-',
                UPPER(TRIM(sku))
            ) ORDER BY updated_at DESC
        ) AS row_id
    FROM
        {{
            get_source(
                "lake",
                "na_sta_cost"
            )
        }}
)

SELECT
    id,
    order_id,
    order_number,
    status,
    product_id,
    sku,
    order_qty,
    packing_qty,
    currency,
    total_price_aud,
    price_cost_aud,
    price_cost_rmb,
    price_cost_usd,
    port_fee_aud,
    charge_item_fee_aud,
    tariff_aud,
    custom_processing_fee_aud,
    other_fee_aud,
    sub_cost_aud,
    total_price_cost_aud,
    total_port_fee_aud,
    total_charge_item_fee_aud,
    total_tariff_aud,
    total_custom_processing_fee_aud,
    total_other_fee_aud,
    total_sub_cost_aud,
    electronic_processing_fee_aud
FROM
    uniq
WHERE
    row_id = 1
