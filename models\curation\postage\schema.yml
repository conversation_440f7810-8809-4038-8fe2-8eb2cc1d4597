version: 2

models:
  - name: competitor_postage_full
    description: "This model contains competitor postage data for analysis."
    columns:
      - name: sku
        description: "The SKU of the product."
        tests:
          - not_null
      - name: url
        description: "The URL of the competitor's product page."
        tests:
          - not_null
      - name: pid
        description: "The product ID of the competitor's product."
      - name: channel
        description: "The channel through which the product is sold."
        tests:
          - not_null
      - name: postcode
        description: "The postcode associated with the competitor's product."
        tests:
          - not_null
      - name: state
        description: "The state where the competitor's product is located."
        tests:
          - not_null
      - name: region_l1
        description: "The first level region of the competitor's product."
        tests:
          - not_null
      - name: region_l2
        description: "The second level region of the competitor's product."
        tests:
          - not_null
      - name: postage
        description: "The postage cost for the competitor's product."
      - name: free_shipping
        description: "Indicates if the competitor's product has free shipping."
      - name: capture_at
        description: "The timestamp when the data was captured."
        tests:
          - not_null
      - name: ingestion_date
        description: "The date when the data was ingested into the system."
        tests:
          - not_null
      - name: id
        description: "A unique identifier for the record."
        tests:
          - not_null
          - unique
      - name: reference_postcode
        description: "The reference postcode used to map postage to all postcode belong to that region l2."
        tests:
          - not_null

  - name: all_channel_shipping_class_unpvt_v2
    description: "The unpivot table of shipping class for all channels using shipping class + national flat rate"
    columns:
      - name: sku
        description: "The SKU of the product."
        tests:
          - not_null
      - name: channel_type
        description: "Which channel the shipping class of the SKU belongs to"
        tests:
          - not_null
      - name: shipping_class
        description: "The shipping class of the SKU on the specific channel"
      - name: national_flat_rate_excl_gst
        description: "The national flat rate (excl GST) for the SKU"      
      - name: generate_date
        description: "The generated date of the data"

  - name: latest_channel_shipping_class_combined
    description: "The combined (new over old) unpivot table of shipping class for all channels using shipping class + national flat rate"
    columns:
      - name: sku
        description: "The SKU of the product."
        tests:
          - not_null
      - name: channel_type
        description: "Which channel the shipping class of the SKU belongs to"
        tests:
          - not_null
      - name: shipping_class
        description: "The shipping class of the SKU on the specific channel"
      - name: national_flat_rate_excl_gst
        description: "The national flat rate (excl GST) for the SKU"      
      - name: generate_date
        description: "The generated date of the data"
    config:
      save_path: "abfss://<EMAIL>/sc-snapshots"
      partition_col: record_date_aueast
