{{ config(
    materialized='incremental',
    unique_key="id",
    incremental_strategy="merge", 
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH unic AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_timestamp DESC) AS row_no
    FROM
        {{ get_source("amazon_api_lake", "amazon_inventory_report") }}
    {% if is_incremental() %}
        WHERE report_date >= (SELECT DATE_SUB(MAX(report_date), 7) FROM {{ this }})
    {% endif %}
)


SELECT
    r_id,
    id,
    report_id,
    item_name,
    item_description,
    listing_id,
    seller_sku,
    price,
    quantity,
    open_date,
    image_url,
    item_is_marketplace,
    product_id_type,
    zshop_shipping_fee,
    item_note,
    item_condition,
    zshop_category1,
    zshop_browse_path,
    zshop_storefront_feature,
    asin1,
    asin2,
    asin3,
    will_ship_internationally,
    expedited_shipping,
    zshop_boldface,
    product_id,
    bid_for_featured_placement,
    add_delete,
    pending_quantity,
    merchant_shipping_group,
    status,
    report_date
FROM unic
WHERE row_no = 1
