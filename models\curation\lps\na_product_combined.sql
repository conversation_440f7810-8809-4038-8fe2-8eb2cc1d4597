{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}


WITH unic AS (
    SELECT
        combined_sku,
        MAX(created_at) AS created_at
    FROM
        {{ ref("na_product_combined_raw") }}
    GROUP BY
        combined_sku
)

SELECT
    a.id,
    a.combined_sku,
    a.combined_name,
    a.currency,
    a.price_aud,
    a.price_rmb,
    a.price_usd,
    a.rate_aud_to_rmb,
    a.rate_aud_to_usd,
    a.status,
    a.creator_id,
    a.creator_cn_name,
    a.creator_en_name,
    a.created_at,
    a.updated_at,
    a.department_id,
    a.department_cn_name,
    a.department_en_name,
    a.ean,
    a.barcode,
    a.flag_sync_status,
    a.flag_sync_date,
    a.combo_type,
    a.category_id,
    a.category_cn_name,
    a.category_en_name,
    a.flag_approve,
    a.owner,
    a.buyer_id,
    a.buyer_cn_name,
    a.buyer_en_name,
    a.buyer_department_id,
    a.buyer_department_cn_name,
    a.buyer_department_en_name,
    a.ab_kit,
    a.common_listing
FROM
    {{ ref("na_product_combined_raw") }} AS a, unic AS b
WHERE
    a.combined_sku = b.combined_sku
    AND
    a.created_at = b.created_at
