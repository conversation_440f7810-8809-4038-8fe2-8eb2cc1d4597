{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('na_view_relate_user_raw') }}


SELECT
    CONCAT_WS(
        "@",
        vendor_id,
        taxonomy_id
    ) AS unic_key,
    vendor_id,
    taxonomy_id,
    vendor,
    taxonomy_name,
    buyer_assistant_id,
    buyer_assistant_name,
    buyer_assistant_email,
    buyer_id,
    buyer_name,
    buyer_email,
    marketing_specialist_id,
    marketing_specialist_name,
    marketing_specialist_email,
    buying_lead_id,
    buying_lead_name,
    buying_lead_email,
    category_lead_id,
    category_lead_name,
    category_lead_email,
    category_manager_id,
    category_manager_name,
    category_manager_email,
    team
FROM
    {{
        ref("na_view_relate_user_raw")
    }}
