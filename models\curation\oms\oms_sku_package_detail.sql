-- This table is come from the SQL Server Stored Procedure [dbo].[SP_Update_OMS_SKU_Package_Detail]
{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    h.package_sku,
    h.package_ean,
    d.sku,
    CASE
        WHEN COALESCE(d.num, 0) = 0 THEN 1
        ELSE d.num
    END AS num,
    d.price
FROM
    {{ ref ("base_sku_package_detail") }} AS d
    LEFT JOIN
        {{ ref ("base_sku_package_header") }} AS h
        ON h.package_id = d.package_id
