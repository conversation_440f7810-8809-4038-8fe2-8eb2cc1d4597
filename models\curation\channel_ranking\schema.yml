version: 2

models:  
  - name: channel_health_check
    description: |
      This table contains results from channel health check crawlers. It tracks all New Aim listings on various channels, recording relevant product and listing information.
    columns:
      - name: id
        description: |
          Unique identifier for each record.
        tests:
          - not_null
          - unique
      - name: title
        description: |
          The title of the product listing on the channel.
      - name: price
        description: |
          The price of the product listed on the channel.
      - name: rating
        description: |
          Average rating of the product as shown on the channel.
      - name: rating_count
        description: |
          The number of ratings or reviews for the product on the channel.
      - name: seller_name
        description: |
          Name of the seller on the channel for this product listing.
      - name: brand_name
        description: |
          Brand name of the product as listed on the channel.
      - name: capture_at
        description: |
          Timestamp of when the crawler captured the data.
        tests:
          - not_null
      - name: image_url
        description: |
          URL to the main image of the product as listed on the channel.
      - name: additional_fields
        description: |
          JSON or map of any additional fields or metadata captured for the listing.
      - name: live_on_site
        description: |
          Indicates whether the product is live on the channel site (boolean: true/false).
      - name: sku
        description: |
          SKU (Stock Keeping Unit) for the product, unique identifier across listings.
        tests:
          - not_null
      - name: pid
        description: |
          Product ID, specific to the channel, identifying this listing.
        tests:
          - not_null
      - name: url
        description: |
          URL of the product listing on the channel.
      - name: channel
        description: |
          Name of the e-commerce channel (e.g., eBay, MyDeal, Bunnings) where the listing is tracked.
        tests:
          - not_null
      - name: response_status
        description: |
          Status code from the health check crawler, indicating if the listing was accessible.