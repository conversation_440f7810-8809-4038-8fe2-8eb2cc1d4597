{{ config(
    materialized = 'incremental',
    partition_by = "ingestion_date",
    incremental_strategy= "insert_overwrite",
    file_format='delta',
    tags = [ "prod", "databricks" , "gcp"]
) }}
SELECT DISTINCT
    rank,
    COALESCE(title, 'UNKNOWN') AS title,
    item_url,
    item_id,
    price,
    sponsored,
    postage,
    page AS page_240,
    CEIL(rank / 60) AS page,
    keyword,
    capture_at,
    seller_name,
    brand_name,
    specifics_item,
    details_item,
    dimension,
    features,
    description_link,
    sold_price,
    sold_price_max,
    variation_sku,
{% if is_incremental() %}
    TO_DATE(FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) AS ingestion_date
FROM
    {{ get_source("lake", "ebay_ranking") }}
WHERE capture_at IS NOT NULL
{% else %}
cast({{ input_file_name_substring(108, 10) }} AS date) AS ingestion_date
FROM {{ get_source("lake", "ebay_ranking_full") }}
    WHERE capture_at IS NOT NUll
{% endif %}
