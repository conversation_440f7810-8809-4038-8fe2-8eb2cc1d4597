{{ config(
    materialized = 'incremental',
    partition_by = "ingestion_date",
    incremental_strategy= "insert_overwrite",
    file_format='parquet' if not check_env_is_gcp() else 'delta',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH catch_marketplace_sku_full AS (
    SELECT DISTINCT
        product_id,
        catch_sku,
        offer_sku
    FROM {{ ref ("catch_marketplace_sku_full") }}
)
SELECT
    ranking.*,
    marketplace_sku.catch_sku,
    marketplace_sku.offer_sku
FROM {{ ref ("catch_ranking_full") }} AS ranking
    LEFT JOIN catch_marketplace_sku_full AS marketplace_sku
        ON ranking.id = marketplace_sku.product_id
WHERE marketplace_sku.offer_sku IS NOT NULL
