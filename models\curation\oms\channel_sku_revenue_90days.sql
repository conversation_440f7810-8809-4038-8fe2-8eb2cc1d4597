{{ config(
    materialized = 'table',
    unique_key = 'id',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('sku_replacement_mapping') }}
-- depends_on: {{ ref('oms_sales_90days') }}
-- depends_on: {{ ref('OMS_channel_price_management') }}
-- depends_on: {{ ref('bridge_unioned_var_listing_common') }}
-- depends_on: {{ ref('ryan_common_sku_postage_shipping_class_all_together') }}
WITH unified AS (
    SELECT
        IF(
            b.sku_name_not_in_use IS NOT NULL,
            b.sku_name_in_use,
            a.sku_name_in_use
        ) AS sku_name_in_use,
        a.sku_name_not_in_use
    FROM
        {{ ref('sku_replacement_mapping') }} AS a
        LEFT JOIN
            {{ ref('sku_replacement_mapping') }} AS b
            ON
                a.sku_name_in_use = b.sku_name_not_in_use
),



listing_sku_revenue AS (
    SELECT
        MAX(a.id) AS id,
        a.tid,
        a.order_no,
        a.customer_order_id,
        a.order_status,
        a.copy_order_status,
        a.order_time,
        a.order_year,
        a.delivery_time AS shipment_time,
        a.delivered_time,
        a.store,
        a.express_mode,
        a.is_swap_order,
        COALESCE(b.sku_name_in_use, a.package_sku) AS sku,
        MIN(a.qty) AS qty,
        MIN(a.listing_qty) AS listing_qty,
        SUM(a.subtotal) AS subtotal,
        SUM(a.unit_cost * a.qty) / MIN(a.qty) AS unit_cost,
        SUM(a.listing_unit_cost * a.listing_qty) / MIN(a.listing_qty) AS listing_unit_cost,
        SUM(a.listing_unit_price * a.listing_qty) / MIN(a.listing_qty) AS unit_price,
        SUM(CASE
            WHEN UPPER(a.store) IN ('WSNS', 'NZHNDS')
                THEN a.listing_unit_price
            WHEN a.currency = 'NZ'
                THEN a.listing_unit_price / 1.15
            ELSE a.listing_unit_price / 1.1
        END * a.listing_qty) / MIN(a.listing_qty) AS unit_price_ex_gst,

        SUM(COALESCE(a.postage_charged, 0) / 1.1
        ) AS postage_charged_ex_gst,

        SUM(CASE
            WHEN UPPER(a.store) = 'WSNS'
                THEN (CASE
                    WHEN UPPER(a.express_mode) = 'NOCARRIER' THEN COALESCE(a.postage_charged, 0)
                    WHEN COALESCE(a.post_est, 0) = 0 THEN COALESCE(a.initial_post_est, 0)
                    ELSE a.post_est
                END)
            ELSE (CASE
                WHEN UPPER(a.express_mode) = 'NOCARRIER'
                    THEN (COALESCE(a.postage_charged, 0) / CAST(1.1 AS DEC(18, 4)))
                WHEN COALESCE(a.post_est, 0) = 0
                    THEN (COALESCE(a.initial_post_est, 0) / CAST(1.1 AS DEC(18, 4)))
                ELSE (a.post_est / CAST(1.1 AS DEC(18, 4)))
            END)
        END) AS postage_cost_ex_gst,
        SUM(a.cost_) AS cost_,
        a.state_,
        a.zipcode,
        a.express_no,
        a.user_nick,
        a.consignee,
        a.phone,
        a.mobile,
        a.suburb,
        a.address,
        a.currency,
        'Listing' AS sku_type,
        a.ismetro,
        SUM(
            IF(
                COALESCE(a.unit_cost, 0) = 0,
                COALESCE(a.cost_, 0),
                a.unit_cost
            ) * a.qty
        ) AS cogs,
        a.cheapest_courier,
        a.smallest_fee,
        a.second_best_courier,
        a.second_smallest_fee,
        a.second_best_additional_post_cost,
        a.second_best_add_post_cost_round_up,
        a.remoteness,
        a.is_split_order
    FROM {{ ref ("oms_sales_90days") }} AS a
        LEFT JOIN
            unified AS b
            ON
                a.package_sku = b.sku_name_not_in_use
    WHERE COALESCE(a.package_sku, '') != ''
    GROUP BY
        a.tid,
        a.order_no,
        a.customer_order_id,
        a.order_status,
        a.copy_order_status,
        a.package_sku,
        a.is_swap_order,
        a.store,
        a.express_mode,
        a.order_time,
        a.order_year,
        a.delivery_time,
        a.delivered_time,
        a.state_,
        a.zipcode,
        a.express_no,
        a.user_nick,
        a.consignee,
        a.phone,
        a.mobile,
        a.suburb,
        a.address,
        a.currency,
        a.ismetro,
        COALESCE(b.sku_name_in_use, a.package_sku),
        a.cheapest_courier,
        a.smallest_fee,
        a.second_best_courier,
        a.second_smallest_fee,
        a.second_best_additional_post_cost,
        a.second_best_add_post_cost_round_up,
        a.remoteness,
        a.is_split_order

    UNION ALL

    SELECT
        a.id,
        a.tid,
        a.order_no,
        a.customer_order_id,
        a.order_status,
        a.copy_order_status,
        a.order_time,
        a.order_year,
        a.delivery_time AS shipment_time,
        a.delivered_time,
        a.store,
        a.express_mode,
        a.is_swap_order,
        COALESCE(b.sku_name_in_use, a.sku) AS sku,
        a.qty,
        a.listing_qty,
        a.subtotal,
        a.unit_cost,
        a.listing_unit_cost,
        a.unit_price,
        CASE
            WHEN UPPER(a.store) IN ('WSNS', 'NZHNDS')
                THEN a.unit_price
            WHEN a.currency = 'NZ'
                THEN a.unit_price / 1.15
            ELSE a.unit_price / 1.1
        END AS unit_price_ex_gst,
        COALESCE(a.postage_charged, 0) / 1.1 AS postage_charged_ex_gst,
        CASE
            WHEN UPPER(a.store) = 'WSNS'
                THEN (CASE
                    WHEN UPPER(a.express_mode) = 'NOCARRIER' THEN COALESCE(a.postage_charged, 0)
                    WHEN COALESCE(a.post_est, 0) = 0 THEN COALESCE(a.initial_post_est, 0)
                    ELSE a.post_est
                END)
            ELSE (CASE
                WHEN UPPER(a.express_mode) = 'NOCARRIER'
                    THEN (COALESCE(a.postage_charged, 0) / CAST(1.1 AS DEC(18, 4)))
                WHEN COALESCE(a.post_est, 0) = 0
                    THEN (COALESCE(a.initial_post_est, 0) / CAST(1.1 AS DEC(18, 4)))
                ELSE (a.post_est / CAST(1.1 AS DEC(18, 4)))
            END)
        END AS postage_cost_ex_gst,
        a.cost_,
        a.state_,
        a.zipcode,
        a.express_no,
        a.user_nick,
        a.consignee,
        a.phone,
        a.mobile,
        a.suburb,
        a.address,
        a.currency,
        'Base' AS sku_type,
        a.ismetro,
        IF(
            COALESCE(a.unit_cost, 0) = 0,
            COALESCE(a.cost_, 0),
            a.unit_cost
        ) * a.qty AS cogs,
        a.cheapest_courier,
        a.smallest_fee,
        a.second_best_courier,
        a.second_smallest_fee,
        a.second_best_additional_post_cost,
        a.second_best_add_post_cost_round_up,
        a.remoteness,
        a.is_split_order
    FROM
        {{ ref ("oms_sales_90days") }} AS a
        LEFT JOIN
            unified AS b
            ON
                a.sku = b.sku_name_not_in_use
    WHERE (a.package_sku IS NULL OR a.package_sku = '')
)
,
amazon_cate_commission AS (
    SELECT
        a.sku,
        a.subcategory_lv3,
        b.amz_cate_commission
    FROM {{ ref("channel_sku_attributes") }} AS a
        LEFT JOIN {{ ref("amz_mapping_for_fee") }} AS b ON a.subcategory_lv3 = b.l3
)
,
listing_sku_revenue_with_listing_measures AS (
    SELECT
        a.id,
        a.tid,
        a.order_no,
        a.customer_order_id,
        a.order_status,
        a.copy_order_status,
        a.order_time,
        a.order_year,
        a.shipment_time,
        a.delivered_time,
        a.store,
        a.express_mode,
        a.is_swap_order,
        a.sku,
        COALESCE(b.listing_sku, a.sku) AS listing_sku,
        CASE WHEN a.sku_type = 'Listing' THEN a.listing_qty ELSE a.qty END AS qty,
        a.subtotal,
        CASE
            WHEN a.listing_unit_cost = 0 AND a.unit_cost = 0 THEN a.cost_
            WHEN a.sku_type = 'Listing' THEN a.listing_unit_cost
            ELSE a.unit_cost
        END AS unit_cost,
        a.unit_price,
        a.unit_price_ex_gst,
        a.postage_charged_ex_gst,
        a.postage_cost_ex_gst,
        a.state_,
        a.zipcode,
        a.express_no,
        a.user_nick,
        a.consignee,
        a.phone,
        a.mobile,
        a.suburb,
        a.address,
        a.currency,
        a.ismetro,
        CASE
            WHEN a.is_swap_order = 'N' OR a.is_swap_order IS NULL
                THEN
                    CASE
                        WHEN a.store IN ('WSNS', 'NZHNDS') THEN a.subtotal
                        WHEN a.currency = 'NZ' THEN a.subtotal / 1.15
                        ELSE a.subtotal / 1.1
                    END + COALESCE(a.postage_charged_ex_gst, 0)
            ELSE 0
        END AS sales_ex_gst_in_different_currency,
        (CASE
            WHEN a.is_swap_order = 'N' OR a.is_swap_order IS NULL
                THEN
                    CASE
                        WHEN a.store IN ('WSNS', 'NZHNDS') THEN a.subtotal + COALESCE(a.postage_charged_ex_gst, 0)
                        WHEN a.currency = 'NZ' THEN a.subtotal / 1.15 + COALESCE(a.postage_charged_ex_gst, 0)
                        WHEN a.store IN ('AMZP', 'AMZFC', 'AMZN') THEN a.subtotal
                        ELSE a.subtotal / 1.1 + COALESCE(a.postage_charged_ex_gst, 0)
                    END
            ELSE 0
        END)
        * CASE
            -- This reference column is coming from the column "Estimated Standard Referral Fee %" in AMZNFC&AMZP Commission Googlesheet
            WHEN a.store IN ('AMZP', 'AMZFC') THEN COALESCE(c.est_discount_fee, d.amz_cate_commission, 0.10)
            -- This reference column is coming from the column "Estimated Discounted Referral Fee %" in AMZNFC&AMZP Commission Googlesheet
            WHEN a.store IN ('AMZN') THEN COALESCE(c.est_discounted_referral_fee, d.amz_cate_commission, 0.13)
            ELSE COALESCE(cm.commission, 0)
        END
        AS commission_in_different_currency,
        a.cogs,
        a.cheapest_courier,
        a.smallest_fee,
        a.second_best_courier,
        a.second_smallest_fee,
        a.second_best_additional_post_cost,
        a.second_best_add_post_cost_round_up,
        a.remoteness,
        --Split Order Status From OMS Order Header, When split order is not 0 then the original order splits into two or more orders
        a.is_split_order
    FROM listing_sku_revenue AS a
        LEFT JOIN {{ ref ("OMS_channel_price_management") }} AS cm
            ON a.order_time >= cm.active_time AND a.order_time < cm.end_time AND a.store = cm.bbcode
        -----------Common SKU-------- Column-------
        LEFT JOIN {{ ref('bridge_unioned_var_listing_common') }} AS b
            ON a.sku = b.variation_sku
        LEFT JOIN {{ ref("amznfc_amzp_commission_scd") }} AS c
            ON a.order_time >= c.valid_from AND a.order_time < c.valid_to AND a.sku = c.sku
        LEFT JOIN amazon_cate_commission AS d
            ON a.sku = d.sku
)
,
listing_sku_revenue_with_extral_measures AS (
    SELECT
        a.*,
        CASE
            WHEN a.currency = 'NZ' THEN CAST(a.sales_ex_gst_in_different_currency / 1.11 AS DOUBLE)
            WHEN a.currency = 'US' THEN CAST(a.sales_ex_gst_in_different_currency * 1.55 AS DOUBLE)
            WHEN a.currency = 'CN' THEN CAST(a.sales_ex_gst_in_different_currency * 0.214 AS DOUBLE)
            ELSE CAST(a.sales_ex_gst_in_different_currency AS DOUBLE)
        END AS sales_ex_gst,
        CASE
            WHEN a.currency = 'NZ' THEN CAST(a.commission_in_different_currency / 1.11 AS DOUBLE)
            WHEN a.currency = 'US' THEN CAST(a.commission_in_different_currency * 1.55 AS DOUBLE)
            WHEN a.currency = 'CN' THEN CAST(a.commission_in_different_currency * 0.214 AS DOUBLE)
            ELSE CAST(a.commission_in_different_currency AS DOUBLE)
        END AS commission
    FROM
        listing_sku_revenue_with_listing_measures AS a
)


SELECT
    *,
    sales_ex_gst - COALESCE(commission, 0) - COALESCE(cogs, 0) - COALESCE(postage_cost_ex_gst, 0) AS profit
FROM
    listing_sku_revenue_with_extral_measures
