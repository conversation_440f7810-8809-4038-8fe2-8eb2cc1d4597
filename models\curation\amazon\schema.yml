version: 2

models:
  - name: amazon_sales_by_child_asin
    columns:
      - name: id
        description: |
          id  
        tests:
          - not_null
          - unique
      - name: report_id
        description: |
          Amazon child asin report id
        tests:
          - not_null
      - name: report_date
        description: |
          Amazon child asin report date
        tests:
          - not_null
      - name: sessions_total
        description: |
          sessions total
        tests:
          - not_null
      - name: created_at
        description: |
          data creation time
        tests:
          - not_null
      - name: updated_at
        description: |
          data updated time
        tests:
          - not_null
      - name: updated_date
        description: |
          partition column
        tests:
          - not_null

  - name: amazon_sales_by_sku
    columns:
      - name: id
        description: |
          id  
        tests:
          - not_null
          - unique
      - name: report_id
        description: |
          Amazon sku sales report id
        tests:
          - not_null
      - name: report_date
        description: |
          Amazon sku report date
        tests:
          - not_null
      - name: sku
        description: |
          Amazon SKU
        tests:
          - not_null
      - name: sessions_total
        description: |
          sessions total
        tests:
          - not_null
      - name: created_at
        description: |
          data creation time
        tests:
          - not_null
      - name: updated_at
        description: |
          data updated time
        tests:
          - not_null
      - name: updated_date
        description: |
          partition column
        tests:
          - not_null

  - name: amazon_click_report
    columns:
      - name: id
        description: |
          id  
        tests:
          - not_null
          - unique
      - name: report_id
        description: |
          Amazon click report id
        tests:
          - not_null
      - name: report_date
        description: |
          Amazon click report date, partition column
        tests:
          - not_null
      - name: asin
        description: |
          Amazon Standard Identification Number
        tests:
          - not_null
      - name: clicks_count
        description: |
          number of click
        tests:
          - not_null
      - name: purchases_count
        description: |
          number of purchase
        tests:
          - not_null
  - name: amazon_inventory_report
    columns:
      - name: id
        description: |
          id  
        tests:
          - not_null
          - unique
      - name: report_id
        description: |
          Amazon inventory report id
        tests:
          - not_null
      - name: report_date
        description: |
          Amazon inventory report date, partition column
        tests:
          - not_null
      - name: item_name
        description: |
          product title
        tests:
          - not_null
      - name: seller_sku
        description: |
          seller product
        tests:
          - not_null
      - name: asin1
        description: |
          Amazon Standard Identification Number
        tests:
          - not_null
      - name: product_id
        tests:
          - not_null
      - name: status
        description: |
          Active, Inactive or Incomplete
        tests:
          - not_null