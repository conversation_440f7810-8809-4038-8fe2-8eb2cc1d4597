version: 2

models:
  - name: produce_line_budgeting_inbound
    description: This table is contains the ingestion data from https://docs.google.com/spreadsheets/d/1P0FnwpcR2YGRSm4JKSKfnSx8ePr-Pq1yH8kry8EceAU/edit?gid=0#gid=0
    columns:
    - name: subcate_id
      description: subcategory id
      tests: 
        - not_null
    - name: vendor_id
      description: supplier id
      tests: 
        - not_null
    - name: yearmonth
      description: forecast time in yyyy-mm format
      tests: 
        - not_null
    - name: inbound
      description: inbound value
      tests: 
        - not_null
    - name: ingestion_date
      description: first ingestion date for this value
      tests: 
        - not_null
    - name: primary_key
      description: unique key, sucate + vendor + yearmonth + ingestion_date
      tests: 
        - not_null
        - unique
    config:
      is_gcp: '{{ env_var("IS_GCP", "false") }}'
  - name: produce_line_budgeting_outbound
    description: This table is contains the ingestion data from https://docs.google.com/spreadsheets/d/1P0FnwpcR2YGRSm4JKSKfnSx8ePr-Pq1yH8kry8EceAU/edit?gid=501857400#gid=501857400
    columns:
    - name: subcate_id
      description: subcategory id
      tests: 
        - not_null
    - name: vendor_id
      description: supplier id
      tests: 
        - not_null
    - name: yearmonth
      description: forecast time in yyyy-mm format
      tests: 
        - not_null
    - name: outbound
      description: outbound value
      tests: 
        - not_null
    - name: ingestion_date
      description: first ingestion date for this value
      tests: 
        - not_null
    - name: primary_key
      description: unique key, sucate + vendor + yearmonth + ingestion_date
      tests: 
        - not_null
        - unique
    config:
      is_gcp: '{{ env_var("IS_GCP", "false") }}'
  - name: produce_line_budgeting_revenue
    description: This table is contains the ingestion data from https://docs.google.com/spreadsheets/d/1P0FnwpcR2YGRSm4JKSKfnSx8ePr-Pq1yH8kry8EceAU/edit?gid=868576409#gid=868576409
    columns:
    - name: subcate_id
      description: subcategory id
      tests: 
        - not_null
    - name: vendor_id
      description: supplier id
      tests: 
        - not_null
    - name: yearmonth
      description: forecast time in yyyy-mm format
      tests: 
        - not_null
    - name: revenue
      description: revenue amount
      tests: 
        - not_null
    - name: ingestion_date
      description: first ingestion date for this value
      tests: 
        - not_null
    - name: primary_key
      description: unique key, sucate + vendor + yearmonth + ingestion_date
      tests: 
        - not_null
        - unique
    config:
      is_gcp: '{{ env_var("IS_GCP", "false") }}'
  - name: produce_line_budgeting_profit
    description: This table is contains the ingestion data from https://docs.google.com/spreadsheets/d/1P0FnwpcR2YGRSm4JKSKfnSx8ePr-Pq1yH8kry8EceAU/edit?gid=1482540845#gid=1482540845
    columns:
    - name: subcate_id
      description: subcategory id
      tests: 
        - not_null
    - name: vendor_id
      description: supplier id
      tests: 
        - not_null
    - name: yearmonth
      description: forecast time in yyyy-mm format
      tests: 
        - not_null
    - name: profit
      description: profit amount
      tests: 
        - not_null
    - name: ingestion_date
      description: first ingestion date for this value
      tests: 
        - not_null
    - name: primary_key
      description: unique key, sucate + vendor + yearmonth + ingestion_date
      tests: 
        - not_null
        - unique
    config:
      is_gcp: '{{ env_var("IS_GCP", "false") }}'
  - name: produce_line_budgeting_kpioutbound
    description: This table is contains the ingestion data from https://docs.google.com/spreadsheets/d/1P0FnwpcR2YGRSm4JKSKfnSx8ePr-Pq1yH8kry8EceAU/edit?gid=2002122948#gid=2002122948
    columns:
    - name: subcate_id
      description: subcategory id
      tests: 
        - not_null
    - name: vendor_id
      description: supplier id
      tests: 
        - not_null
    - name: yearmonth
      description: forecast time in yyyy-mm format
      tests: 
        - not_null
    - name: kpioutbound
      description: kpioutbound amount
      tests: 
        - not_null
    - name: ingestion_date
      description: first ingestion date for this value
      tests: 
        - not_null
    - name: primary_key
      description: unique key, sucate + vendor + yearmonth + ingestion_date
      tests: 
        - not_null
        - unique
    config:
      is_gcp: '{{ env_var("IS_GCP", "false") }}'
  - name: produce_line_budgeting_kpiavgstock
    description: This table is contains the ingestion data from https://docs.google.com/spreadsheets/d/1P0FnwpcR2YGRSm4JKSKfnSx8ePr-Pq1yH8kry8EceAU/edit?gid=912958370#gid=912958370
    columns:
    - name: subcate_id
      description: subcategory id
      tests: 
        - not_null
    - name: vendor_id
      description: supplier id
      tests: 
        - not_null
    - name: yearmonth
      description: forecast time in yyyy-mm format
      tests: 
        - not_null
    - name: kpiavgstock
      description: kpiavgstock amount
      tests: 
        - not_null
    - name: ingestion_date
      description: first ingestion date for this value
      tests: 
        - not_null
    - name: primary_key
      description: unique key, sucate + vendor + yearmonth + ingestion_date
      tests: 
        - not_null
        - unique
    config:
      is_gcp: '{{ env_var("IS_GCP", "false") }}'
    