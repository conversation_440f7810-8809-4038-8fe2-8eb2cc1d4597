{{ config(
    materialized='incremental',
    unique_key="unic_key",
    incremental_strategy="merge",
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH
snapshot_for_inbship AS (
    SELECT
        shipmentnumber AS shipment_number,
        ingestion_date,
        unic_key,
        actualshippingdate,
        actualdeliverydate,
        INLINE(
            FROM_JSON(
                --noqa: disable=PRS
                customfieldlist: customField,
                --noqa: enable=all
                'array<struct<scriptId: string, value: string>>'
            )
        ) AS (field_name, field_value)
    FROM
        {{ ref('netsuite_inbound_shipment_operations') }}
    {% if is_incremental() %}
        WHERE
            ingestion_date >= (SELECT DATE_SUB(MAX(ingestion_date), 7) FROM {{ this }})
    {% endif %}
),

inbship_cust_fields_time_series AS (
    SELECT
        shipment_number,
        ingestion_date,
        unic_key,
        TO_DATE(FROM_UTC_TIMESTAMP(actualshippingdate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS actualshippingdate,
        TO_DATE(FROM_UTC_TIMESTAMP(actualdeliverydate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS actualdeliverydate,
        TO_DATE(FROM_UTC_TIMESTAMP(custrecord_na_readydate, 'Australia/Melbourne'), 'yyyy-mm-dd') AS custrecord_na_readydate,
        TO_DATE(FROM_UTC_TIMESTAMP(custrecord_na_etd, 'Australia/Melbourne'), 'yyyy-mm-dd') AS custrecord_na_etd,
        TO_DATE(FROM_UTC_TIMESTAMP(custrecord_na_eta, 'Australia/Melbourne'), 'yyyy-mm-dd') AS custrecord_na_eta,
        TO_DATE(
            FROM_UTC_TIMESTAMP(custrecord_na_estimatedarrivaldate_1stop, 'Australia/Melbourne'), 'yyyy-mm-dd'
        ) AS custrecord_na_estimatedarrivaldate_1stop,
        --noqa: disable=PRS
        custrecord_na_intransitsubstatus :name AS custrecord_na_intransitsubstatus,
        custrecord_na_inbound_substatus :name AS custrecord_na_inbound_substatus,
        --noqa: enable=all
        custrecord_na_custom_broker
    FROM snapshot_for_inbship
        PIVOT (
                MAX(field_value)
                FOR field_name IN (
                    'custrecord_na_readydate',
                    'custrecord_na_etd',
                    'custrecord_na_intransitsubstatus',
                    'custrecord_na_inbound_substatus',
                    'custrecord_na_custom_broker',
                    'custrecord_na_eta',
                    'custrecord_na_estimatedarrivaldate_1stop'
                )
        )
)

SELECT * FROM inbship_cust_fields_time_series
