{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT
    -- to_date(from_utc_timestamp(current_timestamp(), 'Australia/Melbourne')) AS date_au_mel,
    w.warehouseid,
    LTRIM(RTRIM(i.sku)) AS sku,
    CAST(SUM(COALESCE(i.qty, 0)) AS INT)
    AS qtytotal,
    CAST(SUM(COALESCE(i.qtyallocated, 0)) AS INT)
    AS qtyallocated,
    CAST(SUM(COALESCE(i.qtyonhold, 0)) AS INT)
    AS qtyonhold,
    CAST(SUM(COALESCE(i.qty, 0) - COALESCE(i.qtyallocated, 0) - COALESCE(i.qtyonhold, 0)) AS INT)
    AS qtyavail,
    -- current_timestamp() AS snapshot_datetime_utc
    i.input_files_date
FROM
    {{ ref("full_inv_lot_loc_id") }} AS i
    LEFT JOIN
        {{ ref("full_view_multiwarehouse") }} AS w
        ON i.locationid = w.locationid AND i.input_files_date = w.input_files_date
WHERE
    i.locationid != 'LOST'
    AND i.locationid IS NOT NULL
GROUP BY
    i.input_files_date, LTRIM(RTRIM(i.sku)), w.warehouseid;
