{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp"]
) }}

{% set Suspicious_Count = 5 %}
{% set Suspicious_Referer_Length = 100 %}

-- This query is to load the raw data, parse the json and time 
-- format at once, avoid to load and process multiple times.
WITH dsz_raw_log AS (
    SELECT
        a.id,
        a.data.cookies AS cookies,
        a.data.duid AS duid,
        a.data.ip AS ip,
        a.data.query AS sku_query,
        a.data.referer AS referer,
        a.data.url AS url_involved,
        a.data.user_agent AS user_agent,
        a.message,
        a.transaction_id,
        a.type AS access_type,
        a.date_generated,
        TO_DATE(
            FROM_UTC_TIMESTAMP(
                FROM_UNIXTIME(a.timestamp / 1000, 'yyyy-MM-dd HH:mm:ss'),
                'Australia/Melbourne'
            )
        ) AS datetime_aest
    FROM
        {{ ref('dsz_sku_tracking_log') }} AS a
),

-- Find out the suspicious IP that have more than 5 visits in 1 day
suspicious_visit AS (
    SELECT
        a.ip,
        a.datetime_aest
    FROM
        dsz_raw_log AS a
    GROUP BY
        a.ip,
        a.duid,
        a.datetime_aest
    --Filter IP that have more than 5 visits in 1 day
    HAVING
        COUNT(*) >= {{ Suspicious_Count }}
),

-- Find out the suspicious IP that have referer length >= 100 or referer is NULL
suspicious_web AS (
    SELECT DISTINCT
        a.ip,
        a.datetime_aest
    FROM
        dsz_raw_log AS a
    WHERE
        LEN(TRIM(a.referer)) >= {{ Suspicious_Referer_Length }}
        OR
        a.referer IS NULL
),

--Quarantine Period = Event Occurs 10 Days in advance + 20 Days in the following 
suspicious AS (
    SELECT
        ip,
        datetime_aest,
        DATE_SUB(datetime_aest, 10) AS quarantine_start_day,
        DATE_ADD(datetime_aest, 20) AS quarantine_end_day
    FROM
        suspicious_visit
    UNION
    SELECT
        ip,
        datetime_aest,
        DATE_SUB(datetime_aest, 10) AS quarantine_start_day,
        DATE_ADD(datetime_aest, 20) AS quarantine_end_day
    FROM
        suspicious_web
),

---Filter out suspicious IP Event
-- Use Anti Join to filter out the suspicious IP event
-- Anti Join have much better performance than "NOT IN" or "LEFT JOIN... WHERE ... NULL"
tracking_tmp AS (
    SELECT a.*
    FROM
        dsz_raw_log AS a
        LEFT ANTI JOIN
            suspicious AS b
            ON
                a.ip = b.ip
                AND a.datetime_aest BETWEEN b.quarantine_start_day AND b.quarantine_end_day
    WHERE
        a.user_agent NOT LIKE '%Googlebot%'
        AND
        a.user_agent NOT LIKE '%bingbot%'
        AND
        a.user_agent NOT LIKE '%Baiduspider%'
        AND
        a.user_agent NOT LIKE '%facebookexternalhit%'
        AND
        a.user_agent NOT LIKE '%Applebot%'
        AND
        (a.ip < '*************' OR a.ip > '*************')
        AND
        (a.ip < '************' OR a.ip > '************')
        AND
        (a.ip < '*************' OR a.ip > '*************')
        AND
        (a.ip < '*************' OR a.ip > '*************')
        AND
        -- Azure data centers
        (a.ip < '*************' OR a.ip > '*************')
)

SELECT
    a.id,
    a.cookies,
    a.duid,
    a.ip,
    a.sku_query,
    a.referer,
    a.url_involved,
    a.user_agent,
    a.message,
    a.transaction_id,
    a.access_type,
    a.date_generated,
    a.datetime_aest,
    b.ip_info,
    b.country,
    b.city,
    b.latitude,
    b.longitude,
    b.time_zone,
    b.created_at
FROM
    tracking_tmp AS a
    LEFT JOIN {{ ref("dsz_ip_tracking_info") }} AS b
        ON a.ip = b.ip
WHERE b.country = 'Australia'
