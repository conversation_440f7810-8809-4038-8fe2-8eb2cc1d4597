{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp"]
) }}


WITH amzn_sc AS (
    SELECT
        listing_sku AS sku,
        'amazon_shipping_class' AS channel_type,
        COALESCE(latest_group_no, calc_group_no) AS shipping_class,
        generate_date
    FROM {{ ref('amzn_sku_shipping_class_map') }}
),

bgds_sc AS (
    SELECT
        listing_sku AS sku,
        'bgds_shipping_class' AS channel_type,
        bgds_id AS shipping_class,
        generate_date
    FROM {{ ref('bgds_sku_shipping_class_map') }}
),

buds_sc AS (
    SELECT
        listing_sku AS sku,
        'bunnings_shipping_class' AS channel_type,
        group_no AS shipping_class,
        generate_date
    FROM {{ ref('buds_sku_shipping_class_map') }}
),

bbds_sc AS (
    SELECT
        listing_sku AS sku,
        'baby_bunting_shipping_class' AS channel_type,
        group_no AS shipping_class,
        generate_date
    FROM {{ ref('buds_sku_shipping_class_map') }}
),

msds_sc AS (
    SELECT
        listing_sku AS sku,
        'mysale_shipping_class' AS channel_type,
        group_no AS shipping_class,
        generate_date
    FROM {{ ref('buds_sku_shipping_class_map') }}
),

eopl_sc AS (
    SELECT
        listing_sku AS sku,
        'ebay_shipping_class' AS channel_type,
        COALESCE(latest_group_no, calc_group_no) AS shipping_class,
        generate_date
    FROM {{ ref('eopl_sku_shipping_class_map') }}
),

mdds_sc AS (
    SELECT
        listing_sku AS sku,
        'mydeal_shipping_class' AS channel_type,
        group_no AS shipping_class,
        generate_date
    FROM {{ ref('mdds_sku_shipping_class_map') }}
),

all_chn_tbl AS (
    SELECT *
    FROM amzn_sc
    UNION ALL
    SELECT *
    FROM bgds_sc
    UNION ALL
    SELECT *
    FROM buds_sc
    UNION ALL
    SELECT *
    FROM bbds_sc
    UNION ALL
    SELECT *
    FROM msds_sc
    UNION ALL
    SELECT *
    FROM eopl_sc
    UNION ALL
    SELECT *
    FROM mdds_sc
),

distinct_sku AS (
    SELECT
        sku,
        MAX(generate_date) AS generate_date
    FROM all_chn_tbl
    GROUP BY sku
),

dummy_na AS (
    SELECT
        sku,
        'na_class' AS channel_type,
        NULL AS shipping_class,
        generate_date
    FROM distinct_sku
),

dummy_na_flat AS (
    SELECT
        sku,
        'NA_flat_rate_shipping_class' AS channel_type,
        NULL AS shipping_class,
        generate_date
    FROM distinct_sku
),

dummy_na_perkg AS (
    SELECT
        sku,
        'NA_perkg_rate_shipping_class' AS channel_type,
        NULL AS shipping_class,
        generate_date
    FROM distinct_sku
),

dummy_na_perkg_wtgp AS (
    SELECT
        sku,
        'NA_perkg_wtgp_rate_shipping_class' AS channel_type,
        NULL AS shipping_class,
        generate_date
    FROM distinct_sku
),

dummy_new AS (
    SELECT
        sku,
        'new_shipping_class' AS channel_type,
        NULL AS shipping_class,
        generate_date
    FROM distinct_sku
),

dummy_catch AS (
    SELECT
        sku,
        'catch_shipping_class' AS channel_type,
        NULL AS shipping_class,
        generate_date
    FROM distinct_sku
),

all_chn_tbl_with_dummy AS (
    SELECT *
    FROM all_chn_tbl
    UNION ALL
    SELECT *
    FROM dummy_na
    UNION ALL
    SELECT *
    FROM dummy_na_flat
    UNION ALL
    SELECT *
    FROM dummy_na_perkg
    UNION ALL
    SELECT *
    FROM dummy_na_perkg_wtgp
    UNION ALL
    SELECT *
    FROM dummy_new
    UNION ALL
    SELECT *
    FROM dummy_catch
)

SELECT
    a.sku,
    a.channel_type,
    a.shipping_class,
    b.national_flat_rate_charge AS national_flat_rate_excl_gst,
    a.generate_date
FROM all_chn_tbl_with_dummy AS a
    LEFT JOIN {{ ref('national_flat_rate_table_ex_gst') }} AS b
        ON a.sku = b.listing_sku
