{{ config(
    materialized = 'incremental',
    unique_key = "unique_id",
    incremental_strategy= "merge",
    partition_by = "ingestion_date",
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH raw AS (
    SELECT
        keyword,
        `page`,
        ranking,
        article_id,
        mpn,
        gtin,
        ean,
        title,
        brand,
        variants,
        categories,
        collections,
        image_url,
        `listingStatus` AS listing_status,
        CAST(collectable AS BOOLEAN) AS collectable,
        CAST(deliverable AS BOOLEAN) AS deliverable,
        `maxQuantity` AS max_quantity,
        CAST(price AS FLOAT) AS price,
        CAST(`maxPrice` AS FLOAT) AS max_price,
        CAST(saving AS FLOAT) AS saving,
        CAST(clearance AS BOOLEAN) AS clearance,
        `priceType` AS price_type,
        label,
        CAST(pickup AS BOOLEAN) AS pickup,
        CAST(layby AS BOOLEAN) AS layby,
        `logisticType` AS logistic_type,
        CAST(dsv AS BOOLEAN) AS dsv,
        CAST(stock AS BOOLEAN) AS stock,
        CAST(capture_time AS TIMESTAMP) AS capture_time,
        rating,
        rating_count,
        ingestion_date,
        CONCAT_WS("-", keyword, article_id, ingestion_date) AS unique_id,
        CONCAT(
            "https://www.bigw.com.au/product/t/p/",
            article_id
        ) AS `url`
    FROM {{ get_source("lake", "bigw_ranking_full") }}
    WHERE
        ranking IS NOT NULL
        {% if is_incremental() %}
            AND ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
        {% else %}
            {% if env_var('DBT_ENV', 'dev') == 'dev' %}
                AND ingestion_date >= date_sub(current_date(), 15)
            {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
                AND ingestion_date >= date_sub(current_date(), 60)
            {% endif %}
        {% endif %}

    UNION ALL

    SELECT
        keyword,
        `page`,
        ranking,
        article_id,
        mpn,
        gtin,
        ean,
        title,
        brand,
        variants,
        categories,
        collections,
        image_url,
        `listingStatus` AS listing_status,
        collectable,
        deliverable,
        `maxQuantity` AS max_quantity,
        price,
        `maxPrice` AS max_price,
        saving,
        clearance,
        `priceType` AS price_type,
        label,
        pickup,
        layby,
        `logisticType` AS logistic_type,
        dsv,
        stock,
        capture_time,
        rating,
        NULL AS rating_count,
        ingestion_date,
        CONCAT_WS("-", keyword, article_id, ingestion_date) AS unique_id,
        CONCAT(
            "https://www.bigw.com.au/product/t/p/",
            article_id
        ) AS `url`
    FROM {{ get_source('crawler_delta_lake', 'bigw_ranking_full') }}
    WHERE
        ranking IS NOT NULL AND article_id IS NOT NULL
        {% if is_incremental() %}
            AND ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
        {% else %}
            {% if env_var('DBT_ENV', 'dev') == 'dev' %}
                AND ingestion_date >= date_sub(current_date(), 15)
            {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
                AND ingestion_date >= date_sub(current_date(), 60)
            {% endif %}
        {% endif %}
),

ranked AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY unique_id ORDER BY capture_time DESC) AS row_num
    FROM raw
),

na_brand AS (
    SELECT DISTINCT UPPER(brand) AS brand
    FROM {{ ref("sku_dimension") }}
)

SELECT
    r.* EXCEPT (row_num),
    COALESCE(b.brand IS NOT NULL, FALSE) AS na_product
FROM ranked AS r
    LEFT JOIN na_brand AS b
        ON UPPER(r.brand) = b.brand
WHERE r.row_num = 1
