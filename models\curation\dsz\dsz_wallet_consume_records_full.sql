{{ config(
    materialized='incremental',
    unique_key="id",
    incremental_strategy="merge", 
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH unic AS (
    SELECT
        id,
        order_id,
        customer_id,
        customer_email,
        created_at,
        update_at,
        total_consume_amount,
        rebate_ids,
        remark,
        ingestion_date,
        ingestion_timestamp,
        updated_date,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_timestamp DESC) AS row_id
    FROM
        {{ get_source("lake", "dsz_wallet_consume_records_full") }}
    {% if is_incremental() %}
        WHERE updated_date >= (SELECT DATE_SUB(MAX(updated_date), 7) FROM {{ this }})
    {% endif %}
)


SELECT
    id,
    order_id,
    customer_id,
    customer_email,
    created_at,
    update_at,
    total_consume_amount,
    rebate_ids,
    remark,
    ingestion_date,
    ingestion_timestamp,
    updated_date
FROM unic
WHERE row_id = 1
