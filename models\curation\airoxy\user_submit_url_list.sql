{{ config(
        materialized = "table",
        tags = [ "prod", "databricks", "gcp" ]
) }}

WITH user_submit AS (
    SELECT DISTINCT
        url,
        url_hash
    FROM {{ ref("user_submit_url") }}
),

existing_url AS (
    SELECT DISTINCT `url`
    FROM
        {{ ref("like_for_like_product") }}
    WHERE capture_date >= DATE_SUB(CURRENT_DATE(), 1)
    UNION
    SELECT DISTINCT item_url AS `url`
    FROM
        {{ ref("crawler_products") }}
    WHERE ingestion_date >= DATE_SUB(CURRENT_DATE(), 1)
),

url_list AS (
    SELECT
        a.url,
        a.url_hash,
        CURRENT_DATE AS update_date
    FROM user_submit AS a ANTI JOIN existing_url AS b ON a.url = b.url
),

rm_dup AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY url ORDER BY url) AS row_num
    FROM url_list
)

SELECT * EXCEPT (row_num) FROM rm_dup WHERE row_num = 1
