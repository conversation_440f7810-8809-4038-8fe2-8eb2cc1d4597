{{ config(
    materialized = 'incremental',
    file_format = 'delta',
    incremental_strategy='merge',
    unique_key = 'id',
    parition_by = 'modified_year',
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('dl_ticket_raw') }}
{% if is_incremental() %}
    WITH unic AS (
        SELECT
            *,
            ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_time_utc DESC) AS row_no
        FROM (
            SELECT
                id,
                f_handle_status,
                f_add_time,
                f_order_at,
                f_oms_order_id,
                f_ticket_no,
                f_sku,
                f_order_status,
                f_order_sent,
                f_tracking_number,
                f_handle_mode,
                f_title,
                f_description,
                f_refund_at,
                f_resend_at,
                f_refund_amount,
                f_resend_amount,
                f_department,
                COALESCE(
                    CAST(f_last_updatetime AS TIMESTAMP),
                    CAST(f_add_time AS TIMESTAMP)
                ) AS modified,
                YEAR(
                    COALESCE(
                        CAST(f_last_updatetime AS TIMESTAMP),
                        CAST(f_add_time AS TIMESTAMP)
                    )
                ) AS modified_year,
                ingestion_time_utc
            FROM
                {{ ref('dl_ticket_raw') }}
            WHERE
                COALESCE(
                    CAST(f_last_updatetime AS TIMESTAMP),
                    CAST(f_add_time AS TIMESTAMP)
                ) >= (SELECT MAX(modified) FROM {{ this }} WHERE modified_year >= 2023)
                AND
                f_oms_order_id IS NOT NULL
                AND
                f_oms_order_id != ''
                AND
                f_oms_order_id != '/'
                AND
                f_oms_order_id != '123456789'
        ) AS raw_
    )

    SELECT *
    FROM
        unic
    WHERE
        row_no = 1

{% else %}
WITH unioned AS(
    SELECT
        id,
        f_handle_status,
        f_add_time,
        f_order_at,
        f_oms_order_id,
        f_ticket_no,
        f_sku,
        f_order_status,
        f_order_sent,
        f_tracking_number,
        f_handle_mode,
        f_title,
        f_description,
        f_refund_at,
        f_resend_at,
        f_refund_amount,
        f_resend_amount,
        f_department,
        IFNULL(
            CAST(f_last_updatetime AS TIMESTAMP),
            CAST(f_add_time AS TIMESTAMP)
        ) AS modified,
        ingestion_time_utc
    FROM
        {{ get_source("lake", "dl_ticket_partition") }}
    WHERE
        --On 2023-09-26 A full data ingestion was complete in ADF
        ingestion_date >= '2023-09-27'
    UNION
    SELECT
        id,
        f_handle_status,
        f_add_time,
        f_order_at,
        f_oms_order_id,
        f_ticket_no,
        f_sku,
        f_order_status,
        f_order_sent,
        f_tracking_number,
        f_handle_mode,
        f_title,
        f_description,
        f_refund_at,
        f_resend_at,
        f_refund_amount,
        f_resend_amount,
        f_department,
        IFNULL(
            CAST(f_last_updatetime AS TIMESTAMP),
            CAST(f_add_time AS TIMESTAMP)
        ) AS modified,
        ingestion_time_utc
    FROM
        {{ get_source("lake", "dl_ticket_history") }}
),
unic AS(
    SELECT
        *,
        YEAR(
            modified
        ) AS modified_year,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_time_utc DESC) AS row_no
    FROM
        unioned
    WHERE
        f_oms_order_id IS NOT NULL
    AND
        f_oms_order_id != ''
    AND
        f_oms_order_id != '/'
    AND
        f_oms_order_id != '123456789'
)
SELECT
    *
FROM
    unic
WHERE
    row_no = 1
{% endif %}
