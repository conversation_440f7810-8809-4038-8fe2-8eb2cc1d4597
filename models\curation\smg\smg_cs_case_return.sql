{{ config(
    materialized = 'table',
    indexes=[{'columns': ['id']}],
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH uniq AS (
    SELECT
        id,
        FROM_UTC_TIMESTAMP(FROM_UNIXTIME(f_create_date), 'Australia/Melbourne') AS create_time_aest,
        IF(f_is_ignore = 1, TRUE, FALSE) AS is_ignore,
        f_lastupdatetime AS watermark,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY f_lastupdatetime DESC) AS row_no
    FROM
        {{ ref('dl_cs_case_return') }}
)

SELECT *
FROM
    uniq
WHERE
    row_no = 1
