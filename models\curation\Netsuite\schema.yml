version: 2

models:
  - name: netsuite_vendor_curation
    description: |
      netsuite vendor information. reference: https://3338627.app.netsuite.com/help/helpcenter/en_US/srbrowser/Browser2022_1/script/record/vendor.html
    columns:
      - name: vendor_id
        description: |
          vendor id or internalid
        tests:
          - unique
          - not_null
      - name: terms
        description: |
          local vendor payment terms
  - name: netsuite_inbound_shipment_details
    description: |
      NS inboundShipment details
    columns:
      - name: shipmentnumber
        description: |
          the shipmentnumber generated once the users apply for an inbound shipment
        tests:
          - not_null
      - name: shipmentstatus
        description: |
          the status of this shipment, like 'on water'(to be added as the inboundshipment goes live)
        tests:
          - not_null 
      - name: actualshippingdate
        description: |
          the actual shipment date, only available when the item is shipped
      - name: actualdeliverydate
        description: |
          the actual delivery date of this shipment, the logics will need further confirmation as the inboundshipment goes live
      - name: expecteddeliverydate
        description: |
          the expected delivery date of this shipment, logics to be comfirmed
      - name: expectedshippingdate
        description: |
          the expected shipment date, logics to be comfirmed
      - name: externaldocumentnumber
        description: |
          an ID of the reference document for the shipping team to book shipment
      - name: id
        description: |
          looks unique, logics to be comfirmed
        tests:
          - not_null
      - name: order_id
        description: |
          the PO number in netsuite_purchase_order_curation
        tests:
          - not_null
      - name: sku
        description: |
          base sku 
        tests:
          - not_null
      - name: shipmentitemdescription
        description: |
          logics to be comfirmed
      - name: povendor
        description: |
          the vendor of this PO
        tests:
          - not_null
      - name: receivinglocation
        description: |
          logics to be comfirmed
      - name: quantityreceived
        description: |
          qty received from this shipment & PO & SKU, should not be null
        tests:
          - not_null
      - name: quantityexpected
        description: |
          qty expected to arrive from this shipment & PO & SKU, should not be null
        tests:
          - not_null
      - name: quantityremaining
        description: |
          logics to be comfirmed, should not be null
        tests:
          - not_null
      - name: unit
        description: |
          logics to be comfirmed
      - name: porate
        description: |
          looks like the fob price of this shipment & order & sku? logics to be comfirmed
      - name: expectedrate
        description: |
          looks like the fob price of this shipment & order & sku?(same as above?) logics to be comfirmed
      - name: shipmentitemexchangerate
        description: |
          logics to be comfirmed
      - name: shipmentitemeffectivedate
        description: |
          logics to be comfirmed
      - name: unitlandedcost
        description: |
          looks like the landed cost of this shipment & order & sku? logics to be comfirmed
      - name: totalunitcost
        description: |
          looks like the total cost of this shipment & order & sku?(what's the difference from above?) logics to be comfirmed
      - name: shipmentitemamount
        description: |
          looks like to be equal to 'totalunitcost' * 'quantityexpected'
      - name: currenc
        description: |
          currency of the rlated PO: 'Australian Dollar', 'Chinese Yuan' and 'US Dollar'
      - name: incoterm
        description: |
          looks like the pay terms of this PO? logics to be comfirmed
      - name: shipmentmemo
        description: |
          logics to be comfirmed
      - name: vesselnumber
        description: |
          id of the vessel to ship this sku
  - name: netsuite_purchase_order_curation
    description: |
      A comprehensive table from netsuite_purchase_order_raw. This is an original table to purchase order header and purchase order items
    columns:
      - name: job_number
        description: |
          PO Number 
        tests:
          - not_null
          - unique
      - name: job_status
        description: PO Status
        tests:
          - not_null
      - name: created_date
        description: PO Created Date
        tests:
          - not_null
      - name: modified_aest
        description: Last Modified Date Time in Melboune Time Zone
        tests:
          - not_null
      - name: currency
        description: RMB/ AUD or USD
        tests:
          - not_null
      - name: final_approver
        description: Final Approver
      - name: modified_year
        description: Partition Column
        tests:
          - not_null
  - name: netsuite_inbound_container_curation
    description: |
      NS Inbound Container Information with basic attributes for Inbound Shipment,
      Container, ETA, ETD and actual delivery date
    columns:
      - name: id_
        description: |
          System generated ID
        tests:
          - not_null
          - unique
      - name: ata
        description: |
          Actual Arrival Date. It is the actual date to arrive at the AU Port of Destination
      - name: atd
        description: |
          Actual Departure Date. It is the actual date to depart from CN Port of Origin
      - name: container_size
        description: |
          Container Size
      - name: inbound_shipment
        description: One Container has multiple inboundshipment
        tests:
          - not_null
      - name: ingestion_date
        description: Watermark
        tests:
          - not_null
      - name: ingestion_year
        description: Partition Column
        tests:
          - not_null
  - name: netsuite_inbound_container_items_curation
    description: |
      NS Inbound Items Detail decords SKU level and its qty
    columns:
      - name: internalid
        description: |
          System generated ID
        tests:
          - not_null
          - unique
      - name: seal_number
        description: Seal Number
      - name: sku
        description: Base SKU
        tests:
          - not_null
      - name: ingestion_date
        description: Data Ingestion Date
        tests:
          - not_null
      - name: ingestion_year
        description: Partition Column
        tests:
          - not_null
  - name: netsuite_inbound_shipment_headers
    description: |
      NS inboundShipment headers
    columns:
      - name: shipmentnumber
        description: |
          Unique identifier for each shipment
        tests:
          - not_null
      - name: shipmentstatus
        description: |
          Current status of the shipment [_received, _toBeShipped, _partiallyReceived, _inTransit]
      - name: actualshippingdate
        description: |
          The actual date the shipment was dispatched from the origin port, which is called ATD in NS front end
      - name: actualdeliverydate
        description: |
          The actual date the shipment was delivered to the destination port, which is called ATA in NS front end
      - name: expecteddeliverydate
        description: |
          The expected date of delivery as initially planned, which is called EAD in NS front end
      - name: expectedshippingdate
        description: |
          The expected date of shipment dispatch as initially planned
      - name: vesselnumber
        description: |
          Identifier for the vessel used in the shipping
      - name: vessel_name
        description: |
          Name of the vessel transporting the shipment
      - name: urgent_status
        description: |
          Indicates whether the shipment is marked as urgent
      - name: total_volume_cbm
        description: |
          Total volume of the shipment in cubic meters
      - name: spontime
        description: |
          Timestamp indicating when the shipment was spontaneously updated or modified
      - name: actual_volum_cbm
        description: |
          Actual shipped volume of the shipment in cubic meters
      - name: shipped_container_type
        description: |
          Type of container used for the shipment
      - name: seal_number
        description: |
          Security seal number attached to the shipment container
      - name: confirmed_ready_date
        description: |
          Date on which the shipment was confirmed ready for dispatch
      - name: qc_status
        description: |
          Status of quality control checks(QC) for the shipment. [Tested, Testing In Progress, Exempted]
      - name: contract_port_of_origin
        description: |
          Port to which the shipment was planned to be dispatched based on the contract(PO)
      - name: contract_port_of_destination
        description: |
          Port from which the shipment was planned to be sent based on the contract(PO)
      - name: container_loadingtype
        description: |
          The type of loading method used for the containers [FCL, LCL]
      - name: intransit_substatus
        description: |
          Sub-status providing more details about the shipment while it is in transit |
          [Arrived at AU Port, Not started, On water]
      - name: to_be_shipped_substatus
        description: |
          Sub-status describing the state of the shipment before being dispatched |
          [Passed, Draft, Cancelled, Confirmed, Rejected/Closed]
      - name: freight_rate
        description: |
          The rate charged for the freight transportation
      - name: freight_forwarder
        description: |
          The company responsible for arranging the transportation of the shipment
      - name: etd_week_number
        description: |
          The week number in which the expected time of departure falls
      - name: estimated_departure_date
        description: |
          The planned date for the shipment's departure
      - name: estimated_arrival_date
        description: |
          The planned date for the shipment's arrival at the destination
      - name: custom_broker
        description: |
          The customs broker involved in clearing the goods through customs
      - name: shipped_container_id
        description: |
          The related containers' type, manually updated by the user
      - name: container_qty
        description: |
          Quantity of containers used in the shipment
      - name: booking_vessel_voyage
        description: |
          The specific voyage number of the vessel booked for the shipment
      - name: booking_vessel_name
        description: |
          Name of the vessel booked for transporting the shipment
      - name: booking_container_type
        description: |
          Type of container booked for the shipment
      - name: booking_container_qty
        description: |
          Quantity of containers booked for the shipment
      - name: estimated_arrival_date_1stop
        description: |
          Estimated date of arrival date through the 1-stop api
      - name: port_of_origin
        description: |
          Port from which the shipment was planned to be sent, based on the booking document
      - name: carrier
        description: |
          The transport company that the vessel belongs to
      - name: vendor
        description: |
          The vendor that booked this shipment(and also the one produced the items in most cases)
      - name: freight_type
        description: |
          The method we are charged for the freight[FAK, NAC]
          FAK: Freight All Kinds. Container freight follows the market price provided by the freight_forwarder
          NAC: Named Account Rates. Container freight specially offered to our company, usually lower than FAK
      - name: 20ft_qty
        description: |
          The qty of the 20GP's container arranged in this shipment
      - name: 40ft_qty
        description: |
          The qty of the 40HQ's container arranged in this shipment
      - name: 20ft_freight_rate_usd
        description: |
          The unit prices of the 20GP's container arranged in this shipment
      - name: 40ft_freight_rate_usd
        description: |
          The unit prices of the 20GP's container arranged in this shipment
      - name: free_detention_days
        description: |
          The days offered by the destination port for free detention
      - name: shipping_cost_completed
        description: |
          a flag to tell whether the shipping cost has completed[true, false]
      - name: container_id
        description: |
          The related conatiner numbers, updated by NS automatically once inbound container page is created
  - name: inbship_cust_fields_time_series
    description: |
      This table provides time series data for selected custom fields from the Netsuite inbound shipment operations. 
      The custom fields include 'custrecord_na_readydate', 'custrecord_na_etd', 'custrecord_na_intransitsubstatus', 
      and 'custrecord_na_inbound_substatus', and are converted and pivoted based on the shipment number and ingestion date.
    columns:
      - name: shipment_number
        description: |
          The unique identifier for each shipment.
        tests:
          - not_null
      - name: ingestion_date
        description: |
          The date when the data was ingested.
        tests:
          - not_null
      - name: unic_key
        description: |
          A unique key for identifying each record uniquely(CONCAT_WS(',',id,ingestion_date))
        tests:
          - not_null
          - unique
      - name: actualshippingdate
        description: |
          The atd displayed in NS inbound shipment page.
      - name: actualdeliverydate
        description: |
          The ata displayed in NS inbound shipment page.
      - name: custrecord_na_readydate
        description: |
          The 'custrecord_na_readydate' value, converted to date format and timezone adjusted.
      - name: custrecord_na_etd
        description: |
          The 'custrecord_na_etd' value, converted to date format and timezone adjusted.
      - name: custrecord_na_eta
        description: |
          The 'ESTIMATE TIME ARRIVAL ETA' displayed in NS inbound shipment page.
      - name: custrecord_na_estimatedarrivaldate_1stop
        description: |
          The '1STOP ESTIMATE TIME ARRIVAL' displayed in NS inbound shipment page.
      - name: custrecord_na_intransitsubstatus
        description: |
          The 'custrecord_na_intransitsubstatus' value indicating the in-transit substatus.
      - name: custrecord_na_inbound_substatus
        description: |
          The 'custrecord_na_inbound_substatus' value indicating the inbound substatus.
