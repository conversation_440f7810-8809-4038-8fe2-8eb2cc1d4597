{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('netsuite_inbound_container_raw') }}

/*
1.Bill of Landing Not Reliable
2.Information FROM the owner column do not have clear business purposes
3.Information FROM rectype column do not have clear business purposes
*/

WITH raw_ AS (
    SELECT
        customrecordid AS id_,
        INLINE(
            FROM_JSON(
                --noqa: disable=PRS
                customfieldlist :customfield,
                'array<struct<value:string,internalId:string,scriptId:string>>'
                --noqa: enable=all
            )
        ) AS (
            value_,
            internalid_,
            name_
        ),
        ingestion_date, --Watermark
        ingestion_year
    FROM
        {{ ref('netsuite_inbound_container_raw') }}
    WHERE
        ingestion_date >= (
            SELECT MAX(ingestion_date)
            FROM
                {{ ref('netsuite_inbound_container_raw') }}
            WHERE
                ingestion_year >= YEAR(CURRENT_DATE()) - 1
        )
),

ata AS (
    SELECT
        id_,
        TO_DATE(FROM_UTC_TIMESTAMP(value_, 'Australia/Melbourne')) AS ata
    FROM
        raw_
    WHERE
        internalid_ = 5727
),

atd AS (
    SELECT
        id_,
        TO_DATE(FROM_UTC_TIMESTAMP(value_, 'Australia/Melbourne')) AS atd
    FROM
        raw_
    WHERE
        internalid_ = 5726
),


container_size AS (
    SELECT
        a.id_,
        a.value_:name AS container_size
    FROM
        raw_ AS a
    WHERE
        a.internalid_ = 5728
),

freight_forwarder AS (
    SELECT
        a.id_,
        a.value_:name AS freight_forwarder
    FROM
        raw_ AS a
    WHERE
        a.internalid_ = 5730
),

container_vendor AS (
    SELECT
        a.id_,
        a.value_:name AS container_vendor
    FROM
        raw_ AS a
    WHERE
        a.internalid_ = 5740
),

inbound_shipment AS (
    SELECT
        a.id_,
        a.value_:name AS inbound_shipment
    FROM
        raw_ AS a
    WHERE
        a.internalid_ = 5739
),

container_type AS (
    SELECT
        a.id_,
        a.value_:name AS container_type
    FROM
        raw_ AS a
    WHERE
        a.internalid_ = 5736
),

deliverydate AS (
    SELECT
        id_,
        TO_DATE(FROM_UTC_TIMESTAMP(value_, 'Australia/Melbourne')) AS delivery_date
    FROM
        raw_
    WHERE
        internalid_ = 5734
),

container_name AS (
    SELECT
        id_,
        value_ AS container_name
    FROM
        raw_
    WHERE
        internalid_ = 5737
),

trucking_agent AS (
    SELECT
        a.id_,
        a.value_:name AS trucking_agent
    FROM
        raw_ AS a
    WHERE
        a.internalid_ = 5731
)

SELECT DISTINCT
    a.id_,
    b.ata,
    c.atd,
    f.container_size,
    g.freight_forwarder,
    h.container_vendor,
    i.inbound_shipment,
    j.container_type,
    k.delivery_date,
    l.container_name,
    d.trucking_agent,
    a.ingestion_date,
    a.ingestion_year
FROM
    raw_ AS a
    LEFT JOIN ata AS b ON a.id_ = b.id_
    LEFT JOIN atd AS c ON a.id_ = c.id_
    LEFT JOIN container_size AS f ON a.id_ = f.id_
    LEFT JOIN freight_forwarder AS g ON a.id_ = g.id_
    LEFT JOIN container_vendor AS h ON a.id_ = h.id_
    LEFT JOIN inbound_shipment AS i ON a.id_ = i.id_
    LEFT JOIN container_type AS j ON a.id_ = j.id_
    LEFT JOIN deliverydate AS k ON a.id_ = k.id_
    LEFT JOIN container_name AS l ON a.id_ = l.id_
    LEFT JOIN trucking_agent AS d ON a.id_ = d.id_
