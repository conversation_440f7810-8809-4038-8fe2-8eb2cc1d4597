{{ config(
    materialized = 'incremental',
    file_format = 'delta',
    incremental_strategy='merge',
    unique_key = 'uniq_key',
    parition_by = 'modified_year',
    on_schema_change='append_new_columns',
    pre_hook = "
    {%- set target_relation = adapter.get_relation(
      database=this.database,
      schema=this.schema,
      identifier=this.name) -%}
    {%- set table_exists=target_relation is not none -%}
    {%- if table_exists -%}
                DELETE FROM 
                    {{ this }}
                WHERE
                    modified_year >= YEAR(CURRENT_DATE()) - 1
                AND modified >= (SELECT MIN(modified) FROM {{ref('dl_ticket_sku_30days') }});
    {%- endif -%}",
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('dl_ticket') }}
-- depends_on: {{ ref('dl_ticket_sku_30days') }}

/*
Unlike OMS, dl_ticket_sku does not update data in the original row if data is modified; instead,
 it creates a new row in which dl_ticket_sku is more like a log table, not a fact table.
*/
{% if is_incremental() %}
    SELECT
        *,
        YEAR(modified) AS modified_year
    FROM
        {{ ref('dl_ticket_sku_30days') }}

{% else %}

WITH
unioned AS (
    SELECT
        id,
        f_ticketid,
        f_sku,
        f_job_no,
        f_has_problem,
        f_big_level,
        f_second_level,
        f_problem_reason,
        f_ordered_qty,
        f_unit_cost
    FROM
        {{ get_source('lake','dl_ticket_sku_history') }}
    WHERE
        id IS NOT NULL AND f_ticketid IS NOT NULL AND f_sku IS NOT NULL
    UNION
    SELECT
        id,
        f_ticketid,
        f_sku,
        f_job_no,
        f_has_problem,
        f_big_level,
        f_second_level,
        f_problem_reason,
        f_ordered_qty,
        f_unit_cost
    FROM
        {{ get_source('lake','dl_ticket_sku_partition') }}
    WHERE
        id IS NOT NULL AND f_ticketid IS NOT NULL AND f_sku IS NOT NULL
    --On 2023-09-26, a full ingestion is complete--
    AND ingestion_date >= '2023-09-27'
),
uniq AS(
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY f_ticketid, f_sku ORDER BY id DESC) AS row_id
    FROM
        unioned
)
SELECT
    CONCAT_WS(
        '@',
        a.f_ticketid,
        a.f_sku
    ) AS uniq_key,
    a.f_ticketid,
    a.f_sku,
    a.f_job_no,
    a.f_has_problem,
    a.f_big_level,
    a.f_second_level,
    a.f_problem_reason,
    a.f_ordered_qty,
    a.f_unit_cost,
    b.modified,
    YEAR(b.modified) AS modified_year
FROM
--dl_ticket is a header and dl_ticket is detail , always use inner join to filter out delete id from detail table
    uniq AS a,
    {{ ref('dl_ticket') }} AS b
WHERE
    a.f_ticketid = b.id
    AND a.row_id = 1
{% endif %}
