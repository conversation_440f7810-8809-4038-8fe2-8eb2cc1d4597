{{ config(
    materialized = 'incremental',
    unique_key='id',
    incremental_strategy= "insert_overwrite",
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        ticket_id,
        user_id,
        message_id,
        IF(from_name = 'Prime Cart', 'admin', 'customer') AS role_,
        FROM_UTC_TIMESTAMP(message_created_date, 'Australia/Melbourne') AS created_aest,
        FROM_UTC_TIMESTAMP(replied_date, 'Australia/Melbourne') AS replied_time_aest,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY COALESCE(replied_date, message_created_date) DESC) AS row_id,
        COALESCE(replied_date, message_created_date) AS watermark,
        ingestion_date
    FROM
        {{ ref('dl_mydeal_message') }}
    {% if is_incremental() %}
        WHERE
            ingestion_date >= COALESCE((SELECT MAX(ingestion_date) FROM {{ this }} WHERE ingestion_date > DATE_ADD(CURRENT_DATE(), -7)), '2013-08-01')
    {% endif %}
)

SELECT * EXCEPT (row_id)
FROM
    uniq
WHERE
    row_id = 1
