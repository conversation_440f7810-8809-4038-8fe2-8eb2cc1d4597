{{ config(
    materialized = 'incremental',
    unique_key = 'order_no',
    parition_by = 'order_year',
    file_format = 'delta',
    incremental_strategy='merge',
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}
{% if is_incremental() %}
    WITH unic AS (
        SELECT
            order_no,
            plat_tid,
            tid,
            user_nick,
            consignee,
            phone,
            mobile,
            email,
            address,
            state,
            province,
            city,
            area,
            zipcode,
            remark,
            order_price,
            order_time,
            payment_time,
            invoice_amount,
            distribution_status,
            copy_order_status,
            split_order_status,
            merge_order_status,
            change_order_status,
            split_order_no,
            merge_order_no,
            copy_order_no,
            change_order_no,
            modified,
            copy_order_num,
            YEAR(order_time) AS order_year,
            ROW_NUMBER() OVER (PARTITION BY order_no ORDER BY modified DESC) AS row_no
        FROM
            {{ ref ("order_header") }}
        WHERE
            modified >= (SELECT MAX(modified) FROM {{ this }} WHERE order_year >= YEAR(CURRENT_DATE()) - 1)
    )
{% else %}
WITH unioned AS(
    SELECT
        order_no,
        plat_tid,
        tid,
        user_nick,
        consignee,
        phone,
        mobile,
        email,
        address,
        state,
        province,
        city,
        area,
        zipcode,
        remark,
        order_price,
        order_time,
        payment_time,
        invoice_amount,
        distribution_status,
        copy_order_status,
        split_order_status,
        merge_order_status,
        change_order_status,
        split_order_no,
        merge_order_no,
        copy_order_no,
        change_order_no,
        modified,
        copy_order_num
    FROM {{ ref ("order_header") }}
    UNION
    SELECT
        order_no,
        plat_tid,
        tid,
        user_nick,
        consignee,
        phone,
        mobile,
        email,
        address,
        state,
        province,
        city,
        area,
        zipcode,
        remark,
        order_price,
        order_time,
        payment_time,
        invoice_amount,
        distribution_status,
        copy_order_status,
        split_order_status,
        merge_order_status,
        change_order_status,
        split_order_no,
        merge_order_no,
        copy_order_no,
        change_order_no,
        modified,
        copy_order_num
    FROM {{ ref ("order_header_his") }}
),
unic AS(
    SELECT
        *,
        YEAR(order_time) AS order_year,
        ROW_NUMBER() OVER (PARTITION BY order_no ORDER BY modified DESC) AS row_no
    FROM
        unioned
)
{% endif %}
SELECT *
FROM
    unic
WHERE
    row_no = 1
