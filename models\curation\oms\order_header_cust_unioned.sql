{{ config(
    materialized = 'incremental',
    file_format = 'delta',
    incremental_strategy='merge',
    unique_key = 'order_no',
    parition_by = 'modified_year',
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}

{% if is_incremental() %}
    WITH unic AS (
        SELECT
            order_no,
            customer_order_id,
            delivery_date,
            postage_estimation,
            modified,
            YEAR(modified) AS modified_year,
            ROW_NUMBER() OVER (PARTITION BY order_no ORDER BY modified DESC) AS row_no
        FROM
            {{ ref('order_header_cust') }}
        WHERE
            modified >= (SELECT MAX(modified) FROM {{ this }} WHERE modified_year >= 2023)
            AND order_pos = 0
    )

    SELECT *
    FROM
        unic
    WHERE
        row_no = 1

{% else %}
WITH unioned AS(
    SELECT
        order_no,
        customer_order_id,
        delivery_date,
        postage_estimation,
        modified,
        YEAR(modified) AS modified_year
    FROM
        {{ ref('order_header_cust') }}
    where order_pos = 0
    UNION
    SELECT
        order_no,
        customer_order_id,
        delivery_date,
        postage_estimation,
        modified,
        YEAR(modified) AS modified_year
    FROM
        {{ ref('order_header_cust_his') }}
    where order_pos = 0
),
unic AS(
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY order_no ORDER BY modified DESC) AS row_no
    FROM
        unioned
)

SELECT
    *
FROM
    unic
WHERE
    row_no = 1
{% endif %}
