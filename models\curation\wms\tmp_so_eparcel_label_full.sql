{{ config(
    materialized = 'incremental',
    unique_key='eparcel_label_key',
    incremental_strategy='merge',
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH tmp_so_eparcel_label AS (
    {% if is_incremental() %}
        SELECT
            *,
            CONCAT_WS('-', TRIM(d_edi_05), TRIM(service), TRIM(orderno)) AS eparcel_label_key
        FROM {{ get_source("wms_lake","tmp_so_eparcel_label") }}
    {% else %}
SELECT *,
    concat_ws('-', trim(D_EDI_05), trim(Service), trim(OrderNo)) AS eparcel_label_key
FROM (
SELECT *
FROM {{ get_source("wms_lake","tmp_so_eparcel_label_partition") }}
UNION
SELECT *
FROM {{ get_source("wms_lake","tmp_so_eparcel_label_history") }}
)
{% endif %}
)
,
the_newest_label AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY eparcel_label_key ORDER BY ingestion_time_utc DESC) AS newest_rank
    FROM tmp_so_eparcel_label
)

SELECT
    soreference1,
    TRIM(orderno) AS orderno,
    orderlineno,
    d_edi_02,
    d_edi_03,
    d_edi_04,
    TRIM(d_edi_05) AS d_edi_05,
    d_edi_06,
    service,
    time,
    d_edi_12,
    totalgrossweight,
    totalcubic,
    soreference4,
    print_flag,
    allocationdetailsid,
    pack_flag,
    cageno,
    secondcageno,
    pickwho,
    picktime,
    cageno1,
    pickwho1,
    picktime1,
    province,
    shipwho,
    shiptime,
    datamatrix,
    qty,
    eparcel_label_key
FROM the_newest_label
WHERE newest_rank = 1
