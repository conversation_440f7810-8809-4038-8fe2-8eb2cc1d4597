{{ config(
    materialized = 'incremental',
    unique_key='OrderNo',
    incremental_strategy='merge',
    tags = [ "prod", "databricks", "2hours_refresh", "gcp" ]
) }}

WITH wms_doc_order_header AS (
    SELECT DISTINCT * EXCEPT (edittime_date)
    FROM
        {{ get_source("lake","wms_doc_order_header_partition") }}
    {% if is_incremental() %}
        WHERE
            edittime_date >= (
                SELECT MAX(edittime_date) FROM {{ get_source("lake","wms_doc_order_header_partition") }}
                WHERE edittime_date >= DATE_SUB(CURRENT_DATE(), 5)
            )
    {% else %}
    UNION
    SELECT DISTINCT
        *        
    FROM {{ get_source("lake","wms_doc_order_header_history") }}
{% endif %}
)
,
unic2 AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY TRIM(orderno) ORDER BY ingestion_time_utc DESC) AS unic_rank
    FROM wms_doc_order_header
)

SELECT
    TRIM(orderno) AS orderno,
    ordertype,
    sostatus,
    ordertime,
    expectedshipmenttime1,
    expectedshipmenttime2,
    requireddeliverytime,
    customerid,
    soreference1,
    soreference2,
    soreference3,
    soreference4,
    soreference5,
    releasestatus,
    priority,
    consigneeid,
    consigneename,
    c_address1,
    c_address2,
    c_address3,
    c_address4,
    c_city,
    c_province,
    c_country,
    c_zip,
    c_contact,
    c_mail,
    c_fax,
    c_tel1,
    c_tel2,
    billingid,
    billingname,
    b_address1,
    b_address2,
    b_address3,
    b_address4,
    b_city,
    b_province,
    b_country,
    b_zip,
    b_contact,
    b_email,
    b_fax,
    b_tel1,
    b_tel2,
    deliveryterms,
    deliverytermsdescr,
    paymentterms,
    paymenttermsdescr,
    transportation,
    door,
    route,
    stop,
    placeofloading,
    placeofdischarge,
    placeofdelivery,
    userdefine1,
    userdefine2,
    userdefine3,
    userdefine4,
    userdefine5,
    userdefine6,
    notes,
    h_edi_01,
    h_edi_02,
    h_edi_03,
    h_edi_04,
    h_edi_05,
    h_edi_06,
    h_edi_07,
    h_edi_08,
    h_edi_09,
    h_edi_10,
    carrierid,
    carriername,
    carrieraddress1,
    carrieraddress3,
    carrieraddress2,
    carrieraddress4,
    carriercity,
    carrierprovince,
    carriercountry,
    carrierzip,
    carriercontact,
    carriermail,
    carrierfax,
    carriertel1,
    carriertel2,
    issuepartyid,
    issuepartyname,
    i_address1,
    i_address2,
    i_address3,
    i_address4,
    i_city,
    i_province,
    i_country,
    i_zip,
    i_contact,
    i_mail,
    i_fax,
    i_tel1,
    i_tel2,
    edisendflag,
    edisendtime,
    edisendtime2,
    edisendtime3,
    edisendtime4,
    edisendtime5,
    lastshipmenttime,
    picking_print_flag,
    createsource,
    order_print_flag,
    rfgettask,
    warehouseid,
    erpcancelflag,
    zonegroup,
    singlematch,
    addtime,
    addwho,
    edittime,
    editwho,
    requiredeliveryno,
    medicalxmltime,
    serialnocatch,
    followup,
    userdefinea,
    userdefineb,
    invoiceprintflag,
    invoiceno,
    invoicetitle,
    invoicetype,
    invoiceitem,
    invoiceamount,
    salesorderno,
    puttolocation,
    consigneename_e,
    archiveflag,
    ful_alc,
    deliveryno,
    channel,
    allocationcount,
    expressprintflag,
    deliverynoteprintflag,
    weightingflag,
    udfprintflag1,
    udfprintflag2,
    udfprintflag3,
    waveno,
    ordergroupno,
    cartongroup,
    cartonid,
    h_edi_11,
    h_edi_12
FROM unic2
WHERE unic_rank = 1
