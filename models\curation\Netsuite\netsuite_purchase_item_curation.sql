{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('netsuite_purchase_order_curation') }}
WITH
po_item_detail AS (
    SELECT
        job_number,
        custbody_na_ship_via_lps,
        INLINE(
            FROM_JSON(
                --noqa: disable=PRS
                itemlist :item [*],
                'array<struct<
                item:string,
                line:int,
                quantityOnShipments:double,
                vendorName:string,
                quantityReceived:double' ',
                quantityBilled:double,
                quantityAvailable:double,
                quantityOnHand:double,
                taxCode:string,
                taxRate1:double,
                taxRate2:double' ',
                quantity:double,
                tax1Amt:double,
                grossAmt:double,
                units:string,
                inventoryDetail:string,
                serialNumbers:string,
                description:string' ',
                purchaseContract:string,
                rate:string,
                amount:double,
                options:string,
                taxAmount:double,
                department:string,
                class:string,
                location:string' ',
                landedCostCategory:string,
                customer:string,
                isBillable:string,
                billVarianceStatus:string,
                matchBillToReceipt:string,
                expectedReceiptDate:date' ',
                isClosed:string,
                taxDetailsReference:string,
                createdFrom:string,
                linkedOrderList:string,
                customFieldList:string
                >>'
            )
        ) AS (
            item,
            line_,
            quantity_on_shipments,
            vendor_name,
            quantity_received,
            quantity_billed,
            quantity_available,
            quantity_on_hand,
            tax_code,
            tax_rate1,
            tax_rate2,
            quantity,
            tax1_amt,
            gross_amt,
            units,
            inventory_detail,
            serial_numbers,
            description_,
            purchase_contract,
            rate,
            amount,
            optioncs,
            tax_amount,
            department,
            class,
            location_,
            landed_cost_category,
            customer,
            is_billable,
            bill_variance_status,
            match_bill_to_receipt,
            expected_receipt_date,
            is_closed,
            tax_details_reference,
            created_from,
            linked_order_list,
            custom_field_list
        )
    FROM
        {{ ref('netsuite_purchase_order_curation') }}
),

item_details_add AS (
    SELECT
        job_number,
        item :name AS sku,
        INLINE(FROM_JSON(custom_field_list :customfield[*], 'array<struct<scriptId:string, value:string>>')) AS (scriptid, value_)
    FROM po_item_detail
),

item_details_add_pivot AS (
    SELECT *
    FROM item_details_add PIVOT (
            MAX(value_) FOR scriptid IN (
                'custcol_na_voidposku',
                'custcol_acs_item_barcode'
            )
    )
)

SELECT
    a.job_number,
    a.item:name AS sku,
    -- a.item:internal_id AS sku_id, --no valid values
    a.tax_code:name AS tax_code,
    a.units:name AS units,
    a.line_,
    a.quantity_on_shipments,
    a.quantity_received,
    a.quantity_billed,
    -- a.quantity_available, --no valid values
    -- a.quantity_on_hand, --no valid values
    a.quantity,
    a.tax_rate1,
    -- a.tax_rate2, --no valid values
    a.tax1_amt,
    a.gross_amt,
    -- a.inventory_detail, --no valid values
    -- a.serial_numbers, --no valid values
    a.description_,
    -- a.purchase_contract, --no valid values
    a.rate,
    a.amount,
    -- a.optioncs, --no valid values
    -- a.tax_amount, --no valid values
    a.department,
    -- a.location_, --no valid values
    -- a.landed_cost_category, --no valid values
    -- a.customer, --no valid values
    -- a.is_billable, --no valid values
    -- a.bill_variance_status, --no valid values
    a.match_bill_to_receipt,
    a.expected_receipt_date,
    a.is_closed,
    -- a.tax_details_reference, --no valid values
    -- a.created_from, --no valid values
    -- a.linked_order_list --no valid values
    -- a.custom_field_list,
    COALESCE(b.custcol_na_voidposku, 'false') AS void_flag
FROM
    po_item_detail AS a
    LEFT JOIN item_details_add_pivot AS b ON a.job_number = b.job_number AND a.item:name = b.sku
WHERE
    (
        b.custcol_acs_item_barcode IS NOT NULL -- the POs created in NS naturally
        OR a.custbody_na_ship_via_lps IS FALSE -- the POs created in LPS while shipped in NS
    )
    AND LOWER(a.item:name) NOT LIKE '%service%'
