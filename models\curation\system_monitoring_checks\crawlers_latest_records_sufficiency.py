import datetime
import pytz
import numpy as np
import pandas as pd
from pyspark.sql.functions import regexp_replace

def get_source_path(container, j, year=None, month=None, day=None, is_gcp=False, path_format="date_as_path"):
    if path_format == "latest":
        if is_gcp:
            return f"gs://lake-prod-mr7r/{container}/general_crawlers/{j}/latest.parquet"
        else:
            return f"abfss://{container}@newaimdevadlsaueast.dfs.core.windows.net/general_crawlers/{j}/latest.parquet"
    elif path_format == "ingestion_date":
        if is_gcp:
            return f"gs://lake-prod-mr7r/{container}/general_crawlers/{j}/ingestion_date={year}-{month}-{day}/*/*/*.parquet"
        else:
            return f"abfss://{container}@newaimdevadlsaueast.dfs.core.windows.net/general_crawlers/{j}/ingestion_date={year}-{month}-{day}/*/*/*.parquet"
    else:
        assert(year is not None or month is not None or day is not None)
        if is_gcp:
            return f"gs://lake-prod-mr7r/{container}/general_crawlers/{j}/{year}/{month}/{day}/*.parquet"
        else:
            return f"abfss://{container}@newaimdevadlsaueast.dfs.core.windows.net/general_crawlers/{j}/{year}/{month}/{day}/*.parquet"

def model(dbt, session):
    dbt.config(
        materialized = "table",
        tags = [ "prod", "databricks", "gcp"]
    )

    is_gcp = (dbt.config.get("IS_GCP") == "true")
    if is_gcp:
        container = dbt.config.get("gcp_container")
    else:
        container = dbt.config.get("container")

    crawler_list = ['airoxy/airoxy_user_submit_url', 'amazon-item', 'amazon/item-price-box', 'catch/b2b_catch_list', 'catch/item_price_box', 'dsz_products', 
    'ebay/ebay_my_selling', 'employmenthero', 'exchange_rate', 'jira', 'jira_tempo', 'keypay/roster', 'mydeal/store_sku', 'airoxy/dsz/dsz_sku_product_price',
    'myer/myer_health_check']

    tz = pytz.timezone('Australia/Sydney')
    today = datetime.datetime.now(tz)
    days_range = [today - datetime.timedelta(days = i) for i in range(1, 31)]
    final_result = None
    for j in crawler_list:
        check_result = []
        for i in days_range:
            if len(check_result) == 8:  # Only check the previous eight days' records
                break
            check_day = i.strftime("%Y-%m-%d")
            year = check_day[:4]
            month = check_day[5:7]
            day = check_day[8:]
            try:    # Some crawlers are not catching data every day, so we need to filter the day without data
                if j == "catch/item_price_box":
                    df = session.read.parquet(get_source_path(container, j, year, month, day, is_gcp=is_gcp, path_format="date_as_path"))
                    files_number = df.select("_metadata.file_path")
                    df_mean = df.count() / files_number.distinct().count()
                    check_result.append(df_mean)
                elif j == "airoxy/airoxy_user_submit_url":
                    df = session.read.parquet(get_source_path(container, j, year, month, day, is_gcp=is_gcp, path_format="ingestion_date"))
                    df = df.dropna(subset=["url", "url_hash"])
                    check_result.append(df.count())
                elif j == "airoxy/dsz/dsz_sku_product_price":
                    df = session.read.parquet(get_source_path(container, j, year, month, day, is_gcp=is_gcp, path_format="ingestion_date"))
                    check_result.append(df.count())
                else:
                    df = session.read.parquet(get_source_path(container, j, year, month, day, is_gcp=is_gcp, path_format="date_as_path"))
                    check_result.append(df.count())
            except:
                continue
        if len(check_result) > 0:
            threshold = float(np.mean(check_result) - np.std(check_result) * 3)
        else:
            continue
                            
        current_days_range = [today - datetime.timedelta(days = x) for x in range(0, 10)]
        for day in current_days_range:
            latest_day = day.strftime("%Y-%m-%d")
            year1 = latest_day[:4]
            month1 = latest_day[5:7]
            day1 = latest_day[8:]
            latest_count = 0
            try:    # Not every crawler's latest data date is today, so we need to check the past ten data records until we find the latest data file
                if j == "catch/item_price_box":
                    df1 = session.read.parquet(get_source_path(container, j, is_gcp=is_gcp, path_format="latest"))
                    latest_count = df1.count()
                elif j == "airoxy/airoxy_user_submit_url":
                    df1 = session.read.parquet(get_source_path(container, j, year1, month1, day1, is_gcp=is_gcp, path_format="ingestion_date"))
                    df1 = df1.dropna(subset=["url", "url_hash"])
                    latest_count = df1.count()
                elif j == "airoxy/dsz/dsz_sku_product_price":
                    df1 = session.read.parquet(get_source_path(container, j, year1, month1, day1, is_gcp=is_gcp, path_format="ingestion_date"))
                    latest_count = df1.count()
                else:
                    df1 = session.read.parquet(get_source_path(container, j, year1, month1, day1, is_gcp=is_gcp, path_format="date_as_path"))
                    latest_count = df1.count()
                break
            except:
                continue
        
        try:
            data = [[j, latest_count, threshold, latest_day]]
            each_result = session.createDataFrame(pd.DataFrame(data, columns=['crawler_name', 'data_count', 'threshold', 'latest_date']))
            print(each_result.limit(1))
        except:
            continue

        if final_result is None:
            final_result = each_result
        else:
            final_result = final_result.union(each_result)

    result = final_result.withColumn('crawler_name', regexp_replace('crawler_name', '/', '-'))
    result = result.withColumn('crawler_name', regexp_replace('crawler_name', '_', '-'))

    return result.pandas_api()