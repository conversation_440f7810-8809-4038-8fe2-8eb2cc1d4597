version: 2

sources:
  - name: amazon_api_lake
    schema: parquet
    tables:
      - name: amazon_sales_by_child_asin
        identifier: abfss://<EMAIL>/amazon/sales_by_child_asin
        quoting:
          identifier: true
      - name: amazon_sales_by_sku
        identifier: abfss://<EMAIL>/amazon/sales_by_sku
        quoting:
          identifier: true
      - name: amazon_click_report
        identifier: abfss://<EMAIL>/amazon/click_report
        quoting:
          identifier: true
      - name: amazon_inventory_report
        identifier: abfss://<EMAIL>/amazon/inventory_report
        quoting:
          identifier: true
      