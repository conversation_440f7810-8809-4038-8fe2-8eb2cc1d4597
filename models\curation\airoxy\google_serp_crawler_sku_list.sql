{{ config(
        materialized = "table",
        tags = [ "prod", "gcp", "databricks" ]
) }}


WITH rank_by_date AS (
    SELECT
        sku,
        capture_date,
        ROW_NUMBER() OVER (PARTITION BY sku ORDER BY capture_date DESC) AS date_rank
    FROM {{ ref('dsz_sku_google_result') }}
),

latest_rank_by_date AS (
    SELECT
        sku,
        capture_date AS update_date
    FROM rank_by_date WHERE date_rank = 1
),

active_sku AS (
    SELECT DISTINCT
        d.sku,
        d.title,
        r.update_date
    FROM latest_rank_by_date AS r INNER JOIN {{ ref('dsz_product') }} AS d ON r.sku = d.sku
    WHERE d.vendor_product IS TRUE AND d.active_status IS TRUE
),

new_sku AS (
    SELECT DISTINCT
        d.sku,
        d.title,
        ("1970-01-01") AS update_date
    FROM {{ ref('dsz_product') }} AS d LEFT ANTI JOIN {{ ref('dsz_sku_google_result') }} AS g ON d.sku = g.sku
    WHERE d.vendor_product IS TRUE AND d.active_status IS TRUE
),

sku_list AS (
    SELECT * FROM active_sku
    UNION
    SELECT * FROM new_sku
)

SELECT DISTINCT
    sku,
    title,
    update_date,
    DATE_ADD(update_date, 90) AS next_update_date
FROM sku_list ORDER BY next_update_date
