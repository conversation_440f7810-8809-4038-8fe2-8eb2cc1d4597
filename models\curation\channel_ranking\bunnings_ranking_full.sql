{{ config(
    materialized = 'incremental',
    unique_key = "unique_id",
    partition_by = "ingestion_date",
    incremental_strategy= "merge",
    file_format='delta',
    tags = [ "prod", "databricks", "gcp"]
) }}

WITH result AS (
    SELECT
        raw_data,
        brandname,
        sellername,
        `name`,
        code,
        rating,
        ratingcount,
        price,
        productcount,
        title,
        product_id,
        ranking,
        CEIL(ranking / 36.0) AS `page`,
        keyword,
        `url`,
        capture_at,
        CAST({{ input_file_name_substring(107, 10) }} AS date) AS ingestion_date,
        CONCAT_WS("-", keyword, product_id, CAST({{ input_file_name_substring(107, 10) }} AS date)) AS unique_id
    FROM
        {{ get_source("lake", "bunnings_ranking_full") }}
    WHERE
        ranking IS NOT NULL AND raw_data IS NOT NULL AND capture_at IS NOT NULL
        {% if is_incremental() %}
            AND capture_at >= (SELECT MAX(capture_at) FROM {{ this }})
        {% else %}
            {% if env_var('DBT_ENV', 'dev') == 'dev' %}
                AND capture_at >= date_sub(current_date(), 15)
            {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
                AND capture_at >= date_sub(current_date(), 60)
            {% endif %}
        {% endif %}

    UNION ALL

    SELECT
        raw_data,
        brandname,
        sellername,
        `name`,
        code,
        rating,
        ratingcount,
        price,
        productcount,
        title,
        product_id,
        ranking,
        CEIL(ranking / 36.0) AS `page`,
        keyword,
        `url`,
        capture_at,
        ingestion_date,
        CONCAT_WS("-", keyword, product_id, ingestion_date) AS unique_id
    FROM
        {{ get_source("crawler_delta_lake", "bunnings_ranking_full") }}
    WHERE
        ranking IS NOT NULL AND capture_at IS NOT NULL AND raw_data IS NOT NULL
        {% if is_incremental() %}
            AND ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
        {% else %}
            {% if env_var('DBT_ENV', 'dev') == 'dev' %}
                AND ingestion_date >= date_sub(current_date(), 15)
            {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
                AND ingestion_date >= date_sub(current_date(), 60)
            {% endif %}
        {% endif %}
),

ranked AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY unique_id ORDER BY capture_at DESC) AS row_num
    FROM result
)

SELECT * EXCEPT (row_num) FROM ranked WHERE row_num = 1
