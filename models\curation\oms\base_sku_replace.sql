{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('base_sku_replace_raw') }}
---safety control for Now-- to duplicate data--


WITH sorting AS (
    SELECT
        a.sku_from,
        a.sku_to,
        IF(
            b.sku IS NOT NULL,
            TRUE,
            FALSE
        ) AS has_stock,
        ROW_NUMBER() OVER (PARTITION BY a.sku_from ORDER BY a.created DESC) AS row_id
    FROM
        {{ ref('base_sku_replace_raw') }} AS a
        LEFT JOIN
            {{ ref ('current_base_sku_stock') }} AS b
            ON
                a.sku_to = b.sku
    WHERE
        a.sku_from != a.sku_to AND a.sku_from IS NOT NULL AND a.sku_to IS NOT NULL
),

---A SKU might have two sku_to, A->B and A->C, IF B Still has stock then B > C
ab_to_ac AS (
    SELECT
        sku_from,
        sku_to
    FROM
        sorting
    WHERE
        row_id = 2 AND has_stock IS TRUE
)

SELECT
    sku_from,
    sku_to
FROM
    sorting
WHERE
    row_id = 1
    AND
    sku_from NOT IN (SELECT sku_from FROM ab_to_ac)
UNION
SELECT * FROM ab_to_ac
