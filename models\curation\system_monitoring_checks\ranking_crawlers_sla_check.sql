{{
  config(
    materialized = 'table',
    tags = ['prod', 'databricks', 'gcp']
  )
}}

-- This model is used to monitor the SLA of the ranking crawlers. 
-- It checks the sufficiency of the data and the freshness of the data.
-- The sufficiency check is based on the latest crawled records,
-- which should not be less than 'Mean - 3 * Std' of the past 30 executions.
-- The freshness check is based on the latest crawled records, 
-- which should not exceed 'Refresh frequency * 2' days.


WITH ranked_counts AS (
    SELECT
        crawler_name,
        record_count AS data_count,
        data_date AS latest_date,
        refresh_frequency,
        ROW_NUMBER() OVER (PARTITION BY crawler_name ORDER BY data_date DESC) AS rn
    FROM {{ ref('ranking_crawlers_sufficiency_history') }}
),

historical_stats AS (
    SELECT
        crawler_name,
        AVG(data_count) - 3 * STDDEV(data_count) AS sufficiency_threshold
    FROM ranked_counts
    WHERE rn <= 30
    GROUP BY crawler_name
),

latest_counts AS (
    SELECT
        crawler_name,
        data_count,
        latest_date,
        refresh_frequency
    FROM ranked_counts
    WHERE rn = 1
)

SELECT
    'data' AS department,
    'ranking-crawler-sufficiency-check' AS target,
    CONCAT(
        lc.crawler_name,
        " Sufficiency check: The latest crawled records should 
        not less than 'Mean - Std * 3' of the past 30 executions"
    ) AS item,
    CAST(lc.data_count - hs.sufficiency_threshold AS INT) AS check_result,
    CASE
        WHEN
            lc.data_count - hs.sufficiency_threshold >= 0
            THEN 'PASS'
        ELSE 'ERROR'
    END AS check_status,
    TO_TIMESTAMP(CURRENT_DATE(), 'yyyy-MM-dd') AS updated_at_aueast
FROM
    latest_counts AS lc
    INNER JOIN historical_stats AS hs
        ON lc.crawler_name = hs.crawler_name
UNION
SELECT
    'data' AS department,
    'ranking-crawler-freshness-check' AS target,
    CONCAT(
        crawler_name,
        " Freshness check: The latest crawled records should 
        not exceed 'Refresh frequency * 2' days"
    ) AS item,
    CAST(DATEDIFF(CURRENT_DATE(), latest_date) AS INT) AS check_result,
    CASE
        WHEN
            DATEDIFF(CURRENT_DATE(), latest_date) - refresh_frequency * 2 <= 0
            THEN 'PASS'
        ELSE 'ERROR'
    END AS check_status,
    TO_TIMESTAMP(CURRENT_DATE(), 'yyyy-MM-dd') AS updated_at_aueast
FROM latest_counts
