{{ config(
    materialized = 'incremental',
    unique_key = "unique_id",
    incremental_strategy= "merge",
    partition_by = "ingestion_date",
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH raw AS (
    SELECT
        title,
        stockcode,
        price,
        `wasPrice` AS was_price,
        image_url,
        brand_name,
        seller_name,
        CAST(in_stock AS BOOLEAN) AS in_stock,
        CAST(is_pm_delivery AS BOOLEAN) AS is_pm_delivery,
        CAST(is_for_collection AS BOOLEAN) AS is_for_collection,
        CAST(is_for_delivery AS BOOLEAN) AS is_for_delivery,
        CAST(is_for_express AS BOOLEAN) AS is_for_express,
        variant_sku,
        capture_at,
        keyword,
        ranking,
        `page`,
        ingestion_date,
        barcode,
        CONCAT_WS("-", keyword, stockcode, ingestion_date) AS unique_id,
        CONCAT("https://www.woolworths.com.au/shop/productdetails/", stockcode) AS url
    FROM {{ get_source("lake", "woolworths_ranking_full") }}
    WHERE
        ranking IS NOT NULL
        {% if is_incremental() %}
            AND ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
        {% else %}
            {% if env_var('DBT_ENV', 'dev') == 'dev' %}
                AND ingestion_date >= date_sub(current_date(), 15)
            {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
                AND ingestion_date >= date_sub(current_date(), 60)
            {% endif %}
        {% endif %}

    UNION ALL

    SELECT
        title,
        stockcode,
        price,
        `wasPrice` AS was_price,
        image_url,
        brand_name,
        seller_name,
        in_stock,
        is_pm_delivery,
        is_for_collection,
        is_for_delivery,
        is_for_express,
        NULL AS variant_sku,
        capture_at,
        keyword,
        ranking,
        `page`,
        ingestion_date,
        barcode,
        CONCAT_WS("-", keyword, stockcode, ingestion_date) AS unique_id,
        CONCAT("https://www.woolworths.com.au/shop/productdetails/", stockcode) AS `url`
    FROM {{ get_source("crawler_delta_lake", "woolworths_ranking_full") }}
    WHERE
        ranking IS NOT NULL
        {% if is_incremental() %}
            AND ingestion_date >= (SELECT MAX(ingestion_date) FROM {{ this }})
        {% else %}
            {% if env_var('DBT_ENV', 'dev') == 'dev' %}
                AND ingestion_date >= date_sub(current_date(), 15)
            {% elif env_var('DBT_ENV', 'dev') == 'cicd' %}
                AND ingestion_date >= date_sub(current_date(), 60)
            {% endif %}
        {% endif %}
),

rmv_dup AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY unique_id ORDER BY capture_at DESC) AS row_num
    FROM raw
)

SELECT
    * EXCEPT (row_num),
    COALESCE(LOWER(seller_name) IN ("saver cart", "new aim pty ltd (t/a prime cart)", "prime cart"), FALSE) AS na_product
FROM rmv_dup WHERE row_num = 1
