{{ config(
    materialized = 'incremental',
    partition_by = "ingestion_date",
    incremental_strategy = "insert_overwrite",
    tags = ["prod", "databricks", "gcp" ]
) }}


WITH selling_data AS (
    SELECT DISTINCT
        itemid,
        sku
    FROM {{ ref("ebay_my_selling_full") }}
),


ranking_data AS (
    SELECT *
    FROM {{ ref("ebay_ranking_full") }}
    {% if is_incremental() %}
        WHERE ingestion_date > (
            SELECT MAX(ingestion_date) AS max_date
            FROM {{ this }}
        )
    {% endif %}
)

SELECT
    ranking.*,
    selling.sku
FROM ranking_data AS ranking
    LEFT JOIN selling_data AS selling
        ON ranking.item_id = selling.itemid
WHERE selling.sku IS NOT NULL
