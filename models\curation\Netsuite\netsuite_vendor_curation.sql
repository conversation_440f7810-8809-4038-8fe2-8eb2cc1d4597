{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT DISTINCT *
FROM
    (
        SELECT
            internalid AS vendor_id,
            isperson,
            isinactive,
            FROM_UTC_TIMESTAMP(lastmodifieddate, 'Australia/Melbourne') AS modified_aest,
            TO_DATE(
                FROM_UTC_TIMESTAMP(datecreated, 'Australia/Melbourne')
            ) AS created_date,
            category,
            globalsubscriptionstatus,
            emailpreference,
            vatregnumber,
            payablesaccount,
            terms,
            incoterm,
            balanceprimary,
            unbilledordersprimary,
            currency,
            is1099eligible,
            isjobresourcevend,
            emailtransactions,
            printtransactions,
            faxtransactions,
            externalid,
            accountnumber,
            expenseaccount,
            creditlimit,
            taxitem,
            firstname,
            lastname,
            title,
            --noqa: disable=PRS
            INLINE(
                FROM_JSON(
                    customfieldlist :customfield [*],
                    'array<struct<value:string,scriptId:string>>'
                )
            ) AS (value, customfieldname)
            --noqa: enable=all
        FROM
            {{ ref("netsuite_vendor") }}
    ) PIVOT (
            MAX(value) FOR customfieldname IN (
                'custentityps_vendor_id',
                'custentityps_vendor_code',
                'custentity_na_vendor_service_type',
                'custentity_na_portoforigin',
                'custentity_na_paymentperiod',
                'custentity_na_payment_terms',
                'custentity_na_depositrate',
                'custentity_acs_na_vend_buyer_team',
                'custentity_2663_payment_method',
                'custentity_11724_pay_bank_fees'
            )
    )
