{{ config(
    materialized = 'table',
    tags = [ "prod", "databricks", "gcp" ]
) }}

SELECT DISTINCT
    adult,
    almostgonelimit,
    averagerating,
    brand,
    clubcatcheligible,
    clubcatchexclusive,
    clubcatchprice,
    discountpercent,
    eventid,
    freeshippingavailable,
    id,
    imagelocation,
    images,
    ispersonalisedproduct,
    offerid,
    pricedisplaytype,
    productpath,
    productskus,
    producturl,
    promotiondescription,
    quantityforsale,
    ratingcount,
    retailprice,
    savingslabel,
    selectedofferid,
    selectedofferprice,
    selectedofferquantity,
    selectedoffersellerid,
    sellprice,
    sellable,
    sellerid,
    showclubcatchprice,
    showfromlabel,
    showpromotionprice,
    showpromotiontimer,
    showretailprice,
    showsavingslabel,
    showshortdatedbadge,
    showwaslabel,
    sourceid,
    sourceplatform,
    sourcetype,
    title,
    url,
    wasprice,
    ranking,
    keyword,
    page,
    seller_name,
    capture_at,
    TO_DATE(FROM_UTC_TIMESTAMP(ingestion_time_utc, 'Australia/Melbourne')) AS ingestion_date
FROM {{ get_source("lake", "catch_ranking") }}
WHERE capture_at IS NOT NULL AND id IS NOT NULL
