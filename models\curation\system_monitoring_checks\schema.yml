version: 2

models:
  - name: sku_tracking_crawlers_sla
    description: >
      Tracks the Service Level Agreement (SLA) compliance of SKU tracking crawlers by processing raw data 
      from the channel_health_check table, calculating success and failure rates of requests.
    config:
      tags: ['prod', 'databricks', 'gcp']
    columns:
      - name: id
        description: Unique identifier combining channel, pid and ingestion date
        tests:
          - unique
          - not_null
      - name: ingestion_date
        description: Date when the data was ingested
        tests:
          - not_null
      - name: channel
        description: The channel being monitored
        tests:
          - not_null
      - name: total_requests
        description: Total number of requests made
        tests:
          - not_null
      - name: total_failed
        description: Total number of failed requests
        tests:
          - not_null
      - name: total_success
        description: Total number of successful requests
        tests:
          - not_null
      - name: failed_percentage
        description: Percentage of failed requests
        tests:
          - not_null
      - name: success_percentage
        description: Percentage of successful requests
        tests:
          - not_null

  - name: sku_tracking_crawlers_sla_aggregate
    description: >
      Aggregates SLA data for SKU tracking crawlers and performs statistical checks on various metrics 
      including failed percentage, success percentage, total requests, and data freshness.
    config:
      tags: ['prod', 'databricks', 'gcp']
    columns:
      - name: department
        description: Department responsible for the check (always 'data')
        tests:
          - not_null
      - name: target
        description: System being monitored (always 'channel-health-check')
        tests:
          - not_null
      - name: item
        description: Description of the specific check being performed
        tests:
          - not_null
      - name: check_result
        description: Numerical result of the check
        tests:
          - not_null
      - name: check_status
        description: Status of the check (PASS/ERROR)
        tests:
          - not_null
          - accepted_values:
              values: ['PASS', 'ERROR']
      - name: updated_at_aueast
        description: Timestamp of when the check was performed
        tests:
          - not_null
  - name: ranking_crawlers_sufficiency_history
    description: |
      history for each ranking crawler sufficiency
    config:
      IS_GCP: "{{ env_var('IS_GCP', 'false') }}"
    columns:
      - name: crawler_name
        description: Normalized crawler name
        tests:
          - not_null
      - name: data_date
        description: Date of the data
        tests:
          - not_null
      - name: record_count
        description: Number of records for this crawler on this date
        tests:
          - not_null
      - name: refresh_frequency
        description: Refresh frequency of the crawler
        tests:
          - not_null
      - name: unique_id
        description: Unique key for the record, crawler_name + data_date
        tests:
          - not_null
          - unique
  - name: ranking_crawlers_sla_check
    description: |
      Check the SLA of the ranking crawlers by analyzing daily counts and comparing against thresholds
    columns:
      - name: department
        description: Department responsible for the check (always 'data')
        tests:
          - not_null
      - name: target
        description: System being monitored (always 'channel-health-check')
        tests:
          - not_null
      - name: item
        description: Description of the specific check being performed
        tests:
          - not_null
      - name: check_result
        description: Numerical result of the check
        tests:
          - not_null
      - name: check_status
        description: Status of the check (PASS/ERROR)
        tests:
          - not_null
          - accepted_values:
              values: ['PASS', 'ERROR']
      - name: updated_at_aueast
        description: Timestamp of when the check was performed
        tests:
          - not_null
  - name: crawlers_latest_records_sufficiency
    description: |
      This table checks whether each of the crawlers catches sufficient latest results.
    config:
      container: adf-rawzone
      gcp_container: rawzone
      IS_GCP: "{{ env_var('IS_GCP', 'false') }}"
    columns:
      - name: crawler_name
        description: |
          each crawler name
        tests:
          - not_null
          - unique
      - name: data_count
        description: |
          how many result in the latest crawler data file
        tests:
          - not_null
      - name: threshold
        description: |
          the 15% quartile of the previous <= 8 days count records
        tests:
          - not_null
      - name: latest_date
        description: |
          the latest record date for each crawler
        tests:
          - not_null