version: 2

models:
  - name: na_product
    description: |
      ids.na_product has duplicate sku, this table provide unique key for each sku by inner join sku and created_at
    columns:
      - name: sku
        tests:
          - not_null
          - unique
      - name: status
        description: |
          This Status is system Status not SKU Status, 0 = Draft, 1 = Enable, 2 = Disable, 3 = Delete
        tests:
          - not_null
      - name: sku_status
        description: SKU Status join with sku_status_dictionary
        tests:
          - not_null
      - name: conversion_date
        description: The date that the SKU became the normal SKU (after the develop-risk_control-review process)
      - name: dangerous_good
        description: This flag helps reserve the shipment and streamlines the shipment booking process. 1 = True; 2 = False
      - name: predecessor_product
        description: predecessor_product
      - name: listing_special_request
        description: listing_special_request
      - name: listing_special_label
        description: listing_special_label            
  - name: na_sta_cost
    columns:
      - name: id
        tests:
          - not_null
      - name: order_id
        tests:
          - not_null
      - name: order_number
        tests:
          - not_null
      - name: status
        tests:
          - not_null
      - name: product_id
        tests:
          - not_null
      - name: sku
        tests:
          - not_null
  - name: na_fee_register
    description: Act As Status Table
    columns:
      - name: id
        description: |
          primary_key
        tests:
          - not_null
          - unique
      - name: order_id
        description: |
          Foreign Key to na_cost_product_cost
      - name: status
        tests:
          - not_null
  - name: na_fee_register_product_detail
    columns:
      - name: primary_key
        description: |
          Combination Key  =  business id + id
        tests:
          - not_null
          - unique
      - name: order_id
        description: |
          Foreign Key to na_cost_product_cost
        tests:
          - not_null
      - name: business_id
        tests:
          - not_null
      - name: sku
        description: sku
        tests:
          - not_null
      - name: currency
        description: Three currencies, USD AUD and RMB
        tests:
          - not_null
      - name: price_aud
        description: Price in AUD
        tests:
          - not_null
      - name: price_rmb
        description: price in RMB
        tests:
          - not_null
      - name: price_usd
        description: price in USD
        tests:
          - not_null
  - name: na_user_email
    columns:
      - name: user_name
        description: |
          staff in new aim
        tests:
          - not_null
          - unique
      - name: email
        description: |
          business email address
        tests:
          - not_null
  - name: na_product_combined
    description: Combo SKU and Variation SKU Information, Currently not including Parent SKU
    columns:
      - name: combined_sku
        description: |
          Listing SKU
        tests:
          - not_null
          - unique
      - name: common_listing
        description: Common SKU
      - name: created_at
        description: SKU created Datetime
        tests:
          - not_null
      - name: id
        description: Primary Key
        tests:
          - not_null
          - unique
      - name: combo_type
        description: 1 = Combo SKU, 2 = Variation SKU, 3 = Parent SKU
        tests:
          - not_null
      - name: status
        description: System Status, 0 = Draft, 1 =  Enable, 2 = Disable ,3 = Delete
        tests:
          - not_null
  - name: na_product_combined_combo_var_filtered
    description: |
      Original From na_product_combined with dirty data filter, only Woolie can use this table,
      combined_sku != '=#REF!', id != 'CP2017123123595910001026'
      combo_type IN (1, 2) AND status IN (1, 2)
  - name: combo_base_bridge_after_replacement
    description: |
      na_product_combined_combo_var_filtered + na_product_combined_detail
      It is a bridge between Combo SKU and Base SKU
    columns:
      - name: combined_sku
        description: |
          Listing SKU 
        tests:
          - not_null
      - name: base_sku
        description: Base SKU Consider Replacement
        tests:
          - not_null
      - name: qty_per_base
        description: Qty Per Base
        tests:
          - not_null
      - name: common_listing
        description: Common SKU
        tests:
          - not_null
      - name: ab_kit
        description: MSP Flag, True or False
        tests:
          - not_null
      - name: status
        description: Combo SKU Status, Enable or Disable
        tests:
          - not_null
      - name: ean
        description: EAN
        tests:
          - not_null
      - name: created_at
        description: Created Datetime
        tests:
          - not_null
  - name: na_view_relate_user
    description: |
      Product Category Profile for Buyer Assistant, Buyer, Marketing Specailist, Buying Lead, Cate Lead Cate Manager and their Email and Team
    columns:
      - name: unic_key
        description: |
          Combination Vendor ID + Taxonomy ID
        tests:
          - not_null
          - unique
      - name: vendor_id
        description: Vendor ID
        tests:
          - not_null
      - name: vendor
        description: Product Supplier Name
        tests:
          - not_null
      - name: taxonomy_id
        decription: Taxonomy Id
        tests:
          - not_null
      - name: taxonomy_name
        description: Product Subcategory LV3
        tests:
          - not_null
      - name: team
        description: Handler Team , this column replace na_product.department_en_name, nullable
      - name: buyer_name
        description: Buyer, replace na_product.creator_en_name, nullable
      - name: marketing_specialist_name
        description: Handler Name, replace na_product.handler_en_name, nullable
