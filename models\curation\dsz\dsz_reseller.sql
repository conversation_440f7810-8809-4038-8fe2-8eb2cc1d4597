{{ config(
    materialized = 'incremental',
    file_format = 'delta',
    incremental_strategy='merge',
    unique_key = 'customer_id',
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}

-- depends_on: {{ ref('dsz_reseller_raw') }}

SELECT DISTINCT
    a.customer_id,
    a.ingestion_date,
    a.`group` AS customer_group,
    FROM_UTC_TIMESTAMP(FROM_UNIXTIME(a.created_at), 'Australia/Melbourne') AS created_at_aest,
    a.name AS customer_name,
    a.email,
    IF(
        a.status_txt = '',
        'Pending',
        a.status_txt
    ) AS status_txt,
    a.company,
    CASE
        WHEN COALESCE(a.region, '') = '' THEN 'No State'
        WHEN TRIM(UPPER(a.region)) = 'VICTORIA' THEN 'VIC'
        WHEN TRIM(UPPER(a.region)) = 'NEW SOUTH WALES' THEN 'NSW'
        WHEN TRIM(UPPER(a.region)) = 'QUEENSLAND' THEN 'QLD'
        WHEN TRIM(UPPER(a.region)) = 'AUSTRALIAN CAPITAL TERRITORY' THEN 'ACT'
        WHEN TRIM(UPPER(a.region)) = 'WESTERN AUSTRALIA' THEN 'WA'
        WHEN TRIM(UPPER(a.region)) = 'SOUTH AUSTRALIA' THEN 'SA'
        WHEN TRIM(UPPER(a.region)) = 'TASMANIA' THEN 'TAS'
        WHEN TRIM(UPPER(a.region)) = 'NORTHERN TERRITORY' THEN 'NT'
        WHEN b.state IS NOT NULL THEN 'NZ'
        ELSE 'OTHER COUNTRIES'
    END AS region,
    IF(
        a.account_manager = '',
        NULL,
        a.account_manager
    ) AS account_manager
FROM
    {{ ref('dsz_reseller_raw') }} AS a
    LEFT JOIN
        {{ ref ('nz_state_list') }} AS b
        ON TRIM(UPPER(a.region)) = UPPER(b.state)
{% if is_incremental() %}
    WHERE
        a.ingestion_date >= (
            SELECT MAX(ingestion_date)
            FROM {{ this }}
        )
{% endif %}
