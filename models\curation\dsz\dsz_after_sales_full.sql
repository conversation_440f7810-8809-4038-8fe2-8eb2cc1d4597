{{ config(
    materialized='incremental',
    unique_key="id",
    incremental_strategy="merge", 
    on_schema_change='append_new_columns',
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH unic AS (
    SELECT
        created_at,
        id,
        is_partial_order,
        note,
        reason,
        refund_id,
        retailer_customer_order_id,
        retailer_id,
        retailer_order_id,
        status,
        submit_user_name,
        submit_user_type,
        supplier_id,
        supplier_order_created_at,
        supplier_order_id,
        type,
        updated_at,
        refund,
        GET_JSON_OBJECT(refund, '$.payment_type') AS payment_type,
        GET_JSON_OBJECT(refund, '$.order_amount') AS order_amount,
        GET_JSON_OBJECT(refund, '$.is_full_amount') AS is_full_amount,
        GET_JSON_OBJECT(refund, '$.cancellation_fee') AS cancellation_fee,
        GET_JSON_OBJECT(refund, '$.refund_amount') AS refund_amount,
        GET_JSON_OBJECT(refund, '$.status') AS refund_status,
        supplier_order_info,
        supplier_order_item_info,
        supplier_order_shipping_info,
        request_close_time,
        ingestion_date,
        ingestion_timestamp,
        updated_date,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY ingestion_timestamp DESC) AS row_id
    FROM
        {{ get_source("lake", "dsz_aftersales_full") }}
    {% if is_incremental() %}
        WHERE updated_date >= (SELECT DATE_SUB(MAX(updated_date), 7) FROM {{ this }})
    {% endif %}
)


SELECT
    created_at,
    id,
    is_partial_order,
    note,
    reason,
    refund_id,
    retailer_customer_order_id,
    retailer_id,
    retailer_order_id,
    status,
    submit_user_name,
    submit_user_type,
    supplier_id,
    supplier_order_created_at,
    supplier_order_id,
    type,
    updated_at,
    refund,
    payment_type,
    order_amount,
    is_full_amount,
    cancellation_fee,
    refund_amount,
    refund_status,
    supplier_order_info,
    supplier_order_item_info,
    supplier_order_shipping_info,
    request_close_time,
    ingestion_date,
    ingestion_timestamp,
    updated_date
FROM unic
WHERE row_id = 1
