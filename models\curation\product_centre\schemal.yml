version: 2

models:
  - name: product_centre_listing_sku
    description: |
      From Product Center listing sku only for "Enable" and "In Progress" SKU, exclude "Parent",
      TODO: CHANGE THIS MODEL to another name: "product_center_listing_sku_for_price_review" 
      WARNING: This table only contain "active" and "In Progress" SKU
      PLEASE use table: "product_centre_listing_sku_raw" to get FULL list of Product Center's Listing SKUs
    columns:
      - name: sku
        description: |
          listing sku
        tests:
          - not_null
          - unique
          # - relationships:  Temporaryily comment for migration
          #     to: ref('dynamic_pricing')
          #     field: sku
          #     config:
          #       severity: error
          #       error_if: ">3"
          #       warn_if: ">0"
  - name: product_centre_listing_sku_full
    description: |
      This table include the historical snapshot of Product Center's Listing SKUs
    columns:
      - name: unique_key
        description: |
          listing sku + snapshot date
        tests:
          - not_null
          - unique
  - name: msp_replacement_curation
    description: |
      Similar to Base SKU replace in OMS, This table is derived from product_replacement_incoming_sku,
      MSP Level
    columns:
      - name: msp_old_sku
        description: Original MSP, This is not unique, and old MSP can be replaced multiple times
        tests:
          - not_null
      - name: msp_new_sku
        description: replaced msp sku
        tests:
          - not_null
      - name: create_by
        description: The person who created this records
        tests:
          - not_null
      - name: listing_sku
        description: Listing SKU in Product Cetnre
        tests:
          - not_null

