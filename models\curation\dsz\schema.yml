version: 2

models:
  - name: dsz_after_sales_full
    description: |
      This table record any After Sales request details from DSZ
    columns:
      - name: id
        description: |
          request id  
        tests:
          - not_null
          - unique
      - name: created_at
        description: |
          The time when a request is created in the system in Melbourne time.
        tests:
          - not_null
      - name: submit_user_type
        tests:
          - not_null
      - name: is_partial_order
        description: |
          True or False
        tests:
          - not_null
      - name: supplier_id
        description: |
          supplier id
        tests:
          - not_null
      - name: status
        description: |
          request status
        tests:
          - not_null
      - name: supplier_order_id
        description: |
         supplier order id
        tests:
          - not_null
      - name: type
        description: |
          partial_refund or cancellation
        tests:
          - not_null
      - name: updated_at
        description: |
          The time when a request is updated in the system in Melbourne time.
        tests:
          - not_null
      - name: retailer_id
        tests:
          - not_null
      - name: refund
        description: |
          Refund details
        tests:
          - not_null
      - name: supplier_order_info
        description: |
          Supplier order header
        tests:
          - not_null
      - name: supplier_order_item_info
        description: |
          Supplier order details
        tests:
          - not_null
      - name: ingestion_date
        description: |
          Data ingestion date
        tests:
          - not_null
  - name: dsz_wallet_consume_records_full
    description: |
      This table record DSZ Wallet API (consume-records) data
    columns:
      - name: id
        description: |
          record id  
        tests:
          - not_null
          - unique
      - name: customer_id
        description: |
          Retailer id
        tests:
          - not_null
      - name: order_id
        description: |
          DSZ order id
      - name: customer_email
        description: |
          Retailter email
        tests:
          - not_null
      - name: created_at
        description: |
          Consume records created time.
        tests:
          - not_null
      - name: update_at
        description: |
          Consume records data updated time
        tests:
          - not_null
      - name: ingestion_date
        description: |
          Data ingestion date
        tests:
          - not_null
  - name: dsz_wallet_topup_full
    description: |
      This table record DSZ Wallet API (top-up-history) data
    columns:
      - name: id
        description: |
          record id  
        tests:
          - not_null
          - unique
      - name: customer_id
        description: |
          Retailer id
        tests:
          - not_null
      - name: currency
        tests:
          - not_null
      - name: payment_method
        description: |
          payment method
        tests:
          - not_null
      - name: transaction_id
        description: |
          transaction id
        tests:
          - not_null
      - name: credit_handling_type_text
        description: |
          Credit Recharge, Credit Adjustment or unknown
        tests:
          - not_null
      - name: credit_process_type_text
        tests:
          - not_null
      - name: created_time
        description: |
          Top-up created time.
        tests:
          - not_null
      - name: updated_time
        description: |
          Top-up data updated time
        tests:
          - not_null
      - name: ingestion_date
        description: |
          Data ingestion date
        tests:
          - not_null
  - name: dsz_order_full
    description: |
      An order transaction table, the data is extracted through API from Dropshipzone. This table is equivalent to the oms sale fact. 
    columns:
      - name: increment_id
        description: |
          System generated primary key  
        tests:
          - not_null
          - unique
      - name: created_at_aest
        description: |
          The time when a transaction record is created in the system, It is also the order datetime from Magento front end system.
        tests:
          - not_null
      - name: items
        description: |
          Array column that contains most important information for business purposes, A further transformation is proceeded in the fact schema
      - name: ingestion_at_utc
        description: |
          The time when data is ingested to Azure Storage
        tests:
          - not_null
      - name: updated_at_utc
        description: |
          Watermark column IFNULL(update, create)
        tests:
          - not_null
  - name: dsz_vendor_bill_full
    description: |
      An order transaction table, the data is extracted through API from Dropshipzone. 
    columns:
      - name: order_date
        description: |
          dsz order date
        tests:
          - not_null
      - name: sku
        description: |
          DSZ product sku 
        tests:
          - not_null
      - name: type
        description: |
          adjustment, bill, credit
        tests:
          - not_null
      - name: vendor_id
        tests:
          - not_null
      - name: date
        tests:
          - not_null
      - name: file_name
        description: |
          location on Azure data lake
        tests:
          - not_null
  - name: dsz_sku_tracking_ip_filterred
    description: |
      The filterred SKU tracking info.
    columns:
      - name: id
        description: |
          The ID of the tracking info
      - name: cookies
        description: |
          The content of cookies if available
      - name: duid
        description: |
          The DHCP unique identifier
      - name: ip
        description: |
          IP address
