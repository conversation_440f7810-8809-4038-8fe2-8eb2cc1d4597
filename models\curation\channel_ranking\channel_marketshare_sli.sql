{{ config(
    materialized = "incremental",
    partition_by = "ingestion_date",
    unique_key = "id",
    incremental_strategy = "merge",
    tags = [ "prod", "databricks", "gcp" ]
) }}

WITH search_cat AS (
    SELECT DISTINCT
        lv3_subcategory_id,
        lv3_subcategory,
        ingestion_date
    FROM {{ ref("terapeak_search_criteria_taxonomy_lv3_full") }}
    {% if is_incremental() %}
        WHERE ingestion_date > (SELECT MAX(ingestion_date) FROM {{ this }})
    {% endif %}
),

channel_date AS (
    SELECT DISTINCT
        channel,
        ingestion_date
    FROM
        {{ ref("channel_marketshare") }}
),

channel_search_cat AS (
    SELECT
        cd.channel,
        sc.lv3_subcategory_id,
        sc.lv3_subcategory,
        cd.ingestion_date
    FROM channel_date AS cd LEFT JOIN search_cat AS sc ON cd.ingestion_date = sc.ingestion_date
    WHERE sc.lv3_subcategory_id IS NOT NULL
),

crawler_result AS (
    SELECT DISTINCT
        channel,
        lv3_subcategory_id,
        ingestion_date
    FROM {{ ref("channel_marketshare") }}
    {% if is_incremental() %}
        WHERE ingestion_date > (SELECT MAX(ingestion_date) FROM {{ this }})
    {% endif %}
),

sli_aggreated AS (
    SELECT
        CONCAT_WS('-', csc.channel, csc.lv3_subcategory, csc.ingestion_date) AS id,
        csc.channel,
        csc.lv3_subcategory_id,
        csc.lv3_subcategory,
        csc.ingestion_date,
        COALESCE(cr.lv3_subcategory_id IS NOT NULL, FALSE) AS is_crawled,
        LAG(cr.ingestion_date) OVER (
            PARTITION BY csc.lv3_subcategory_id, csc.channel
            ORDER BY csc.ingestion_date
        ) AS previous_ingestion_date
    FROM
        channel_search_cat AS csc
        LEFT JOIN
            crawler_result AS cr
            ON csc.lv3_subcategory_id = cr.lv3_subcategory_id AND csc.ingestion_date = cr.ingestion_date AND csc.channel = cr.channel
)

SELECT
    id,
    channel,
    lv3_subcategory,
    ingestion_date,
    is_crawled,
    CASE
        WHEN is_crawled = TRUE THEN TRUE
        WHEN previous_ingestion_date IS NOT NULL THEN TRUE
        ELSE FALSE
    END AS is_fresh
FROM sli_aggreated
