{{ config(
    materialized='incremental',
    unique_key="increment_id",
    incremental_strategy="merge",
    on_schema_change='append_new_columns',
    tags = ["databricks", "gcp"]
) }}

-- tag: prod and full_refresh have been removed by Clark, 2025-01-29

WITH unic AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY increment_id ORDER BY updated_at DESC) AS row_id
    FROM
        {{ get_source("lake", "dsz_supplier_order_full") }}
    {% if is_incremental() %}
        WHERE
            updated_date >= COALESCE((SELECT MAX(updated_date) FROM {{ this }} WHERE updated_date > DATE_ADD(CURRENT_DATE(), -7)), "2022-12-01")
    {% endif %}
)


SELECT *
FROM
    unic
WHERE
    row_id = 1
    AND increment_id IS NOT NULL
