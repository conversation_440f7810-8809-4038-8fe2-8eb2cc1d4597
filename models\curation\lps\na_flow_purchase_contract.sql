{{ config(
    materialized='table',
    indexes=[{'columns': ['id']}],
    unique_key = 'primary_key',
    tags = [ "prod", "databricks", "gcp" ]
) }}


SELECT
    id,
    order_date,
    order_number,
    order_title,
    vendor_id,
    vendor_en_name,
    currency,
    CASE
        WHEN currency = 1 THEN total_price_aud
        WHEN currency = 2 THEN total_price_rmb
        ELSE total_price_usd
    END AS purchase_contract_total_price,
    total_cbm,
    origin_port_en_name,
    deposit_rate,
    created_at,
    updated_at,
    start_time,
    end_time,
    creator_en_name,
    department_en_name,
    status,
    flow_status,
    eta,
    deposit_type,
    trade_term
FROM
    {{
    get_source(
        "lake",
        "na_flow_purchase_contract"
    )
}}
