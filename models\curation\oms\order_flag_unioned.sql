{{ config(
     materialized = 'incremental',
    unique_key = 'order_no',
    parition_by = 'modified_year',
    file_format = 'delta',
    incremental_strategy='merge',
    tags = [ "prod", "databricks", "gcp" ]
) }}
-- depends_on: {{ ref('order_flag') }}
{% if is_incremental() %}
    SELECT
        *,
        YEAR(modified) AS modified_year
    FROM
        {{ ref('order_flag') }}
    WHERE
        modified >= (SELECT MAX(modified) FROM {{ this }})

{% else %}
WITH unioned AS(
    SELECT
        order_no,
        flag,
        modified
    FROM {{ ref ("order_flag_his") }}
    UNION
    SELECT
        order_no,
        flag,
        modified
    FROM {{ get_source('lake','order_flag_partition') }}
),
uniq AS(
    SELECT
        order_no,
        flag,
        modified,
        ROW_NUMBER() OVER (PARTITION BY order_no ORDER BY modified DESC) AS row_id
    FROM
        unioned
)
SELECT
    order_no,
    flag,
    modified,
    YEAR(modified) AS modified_year
FROM
    uniq
WHERE
    row_id  = 1
{% endif %}
