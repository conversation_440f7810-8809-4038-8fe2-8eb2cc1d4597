{{ config(
    materialized='table',
    tags = [ "prod", "databricks", "gcp" ]
) }}
WITH uniq AS (
    SELECT
        id,
        f_loginname AS login_name,
        UPPER(TRIM(f_name)) AS user_name,
        FROM_UTC_TIMESTAMP(
            FROM_UNIXTIME(f_last_logintime),
            'Australia/Melbourne'
        ) AS last_login,
        IF(f_status = 1, 'Active', 'Inactive') AS user_status,
        IF(f_cs_group = '', 'No Group', REPLACE(f_cs_group, '_group', '')) AS cs_group,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY id) AS row_id
    --Optional Columns
    /*
    f_parent_id,
    f_password,
    f_user_roleid,
    f_gender,
    f_phone,
    f_company,
    f_email,
    f_login_count,
    f_create_time,
    f_config,
    f_action_config,
    f_subordinate,
    f_platform,
    f_department,
    f_team,
    */
    FROM
        {{ get_source('lake','dl_cs_user') }}
    WHERE
        f_name NOT IN ('Admin', 'IT-test01', 'IT-test02')
)

SELECT *
FROM
    uniq
